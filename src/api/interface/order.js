
import { getBizCode } from '@utils/curEnv.js'
import {formPost} from "@api/config/index.js";
import { curChannelBiz } from '@utils/storage.js'

// 京东地址检查
export const jdAddressCheck = () => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/check').then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '地址检查失败' }, null])
  })
})

// 下单
export const submitOrder = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/create/common', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '下单失败' }, null])
  })
})

// 查询待付款、待发货、待收货订单数量
export const getOrderCount = () => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderListCount', { disriBiz: getBizCode('ORDER') }, {}, {
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '下单失败' }, null])
  })
})

// 订单列表
export const getOrderList = (data) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderList/common', {
    orderListVoStr: JSON.stringify(data)
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单列表数据获取失败' }, null])
  })
})

// 搜索订单列表
export const getOrderSearchList = (data) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderListBySearch', {
    orderListVoStr: JSON.stringify(data)
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '搜索订单列表数据获取失败' }, null])
  })
})

// 取消订单
export const cancelOrder = (orderId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/cancel', { orderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '取消订单' }, null])
  })
})

// 订单重新支付
export const repayOrder = (orderId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/repay', { orderId, disriBiz: getBizCode('ORDER'), bizChannelCode: curChannelBiz.get() }).then(res => {
    resolve([res, res.data])
  }).catch(() => {
    resolve([{ code: -1, msg: '订单重新支付失败' }, null])
  })
})

// 订单详情
// 如果是未支付orderState==='0'，此时必须isPay=1，其他状态可以不传
export const getOrderInfo = (orderId, isPay) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderInfo', { orderId, isPay }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单详情获取失败' }, null])
  })
})

// 查询订单快递信息
export const getOrderExpress = (supplierOrderId, roleType = '') => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderListByDeliverInfo', { supplierOrderId, roleType, bizCode: getBizCode() }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询订单快递信息失败' }, null])
  })
})

// 查询售后客户回寄快递信息
export const getAfterSalesExpress = (applySaleApplyId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/queryUserReturnExpressTrack', { applySaleApplyId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询售后客户回寄快递信息失败' }, null])
  })
})

// 查询快递信息
export const getExpress = (supplierSubOrderId, expressNo, roleType = '') => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderExpressage', { supplierSubOrderId, expressNo, roleType }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询快递信息失败' }, null])
  })
})

// 获取售后列表
export const getAfterSalesInfo = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后列表失败' }, null])
  })
})

// 获取售后单详情
export const getOrderAfterSalesInfo = (afterSaleId, bizCode) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/detail', { afterSaleId, bizCode }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后单详情失败' }, null])
  })
})

// JD申请售后或者查看售后页面
export const applyOrderAfterSalesJD = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/getSaleComponentUrl', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后URL失败' }, null])
  })
})

// 自营商城申请退款
export const applyOrderRefund = supplierSubOrderId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/apply', { supplierSubOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请失败' }, null])
  })
})

// JDMall/华泊商城申请退款
export const applyOrderRefundSupplierOrder = supplierSubOrderId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/supplierOrder/cancel', { supplierSubOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请失败' }, null])
  })
})
// 供应商申请退款
export const afterSaleSupplierOrderRefund = supplierSubOrderId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/supplierOrder/refund', { supplierSubOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请失败' }, null])
  })
})
// 订单详情获取售后按钮的类型
export const getOrderDetailsAfterSalesButton = supplierSubOrderId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/button', { supplierSubOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后类型失败' }, null])
  })
})

// 根据订单ID获取售后服务单ID
export const getSupplierSubOrderIdToAfterSaleId = supplierSubOrderId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/supplierSubOrderId/afterSaleId', { supplierSubOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后服务单ID失败' }, null])
  })
})

// 敏感词校验
export const isCheckSensitiveWord = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/isCheckSensitiveWord', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '敏感词校验失败' }, null])
  })
})

// 订单商品校验
export const isCheckGoodsExistsBol = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/isCheckGoodsExists', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单商品校验失败' }, null])
  })
})

// 订单确认收货
export const manualConfirmRecv = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/manualConfirmRecv', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单确认收货失败' }, null])
  })
})

// 额度不可用活动
export const notApplicableQuotaWish = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/quota/getNotApplicableQuotaWish', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询额度不可用活动失败' }, null])
  })
})
export const getNotApplicableQuotaWish = notApplicableQuotaWish

// 额度不可用活动
export const queryAmount = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/quota/queryAmount', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询额度可用活动失败' }, null])
  })
})

export const getQueryAmount = queryAmount

// 获取回收站数据
export const getOrderRecycleBinList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getOrderRecycleBinList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取回收站数据失败' }, null])
  })
})

// 订单删除恢复
export const modOrderListShow = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/modOrderListShow', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单删除恢复失败' }, null])
  })
})

// 订单再次购买检查
export const verifySupplierOrderRepurchased = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/verifySupplierOrderRepurchased', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, res.data])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '订单再次购买检查失败' }, null])
  })
})

// 本地取消售后
export const cancelAfterSales = (afterSaleId, bizCode) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/cancel', { bizCode, afterSaleId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取售后列表失败' }, null])
  })
})

// 订单地址修改
export const modifyOrderAddress = (orderAddressRecord) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/address/update', { orderAddressRecord }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  })
})

// 京东订单地址修改检查
export const jdModifyOrderAddress = (orderId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/jd/address/check', { orderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东地址修改检查失败' }, null])
  })
})
