/**
 * 物流状态语义分析器
 * 根据物流轨迹信息智能分析当前物流状态
 * 集成 compromise 库进行增强语义分析
 */

import nlp from 'compromise'

// 物流状态枚举
export const LOGISTICS_STATUS = {
  PENDING_PICKUP: 'pending_pickup',     // 待揽件
  PICKED_UP: 'picked_up',               // 已揽件
  IN_TRANSIT: 'in_transit',             // 运输中
  OUT_FOR_DELIVERY: 'out_for_delivery', // 派送中
  DELIVERED: 'delivered',               // 已签收
  FAILED_DELIVERY: 'failed_delivery',   // 派送失败
  RETURNED: 'returned',                 // 已退回
  EXCEPTION: 'exception'                // 异常
}

// 状态显示文本映射
export const STATUS_TEXT_MAP = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: '待揽件',
  [LOGISTICS_STATUS.PICKED_UP]: '已揽收',
  [LOGISTICS_STATUS.IN_TRANSIT]: '运输中',
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中',
  [LOGISTICS_STATUS.DELIVERED]: '已签收',
  [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败',
  [LOGISTICS_STATUS.RETURNED]: '已退回',
  [LOGISTICS_STATUS.EXCEPTION]: '异常'
}

// 关键词匹配规则 - 按权重分级，增强版
const STATUS_KEYWORDS = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: {
    high: ['待揽收', '等待揽收', '待收件', '等待收件', '订单已提交', '已下单待揽收', '等待揽件', '待揽件', '等待取件人', '寄件人已下单', '等待快递员上门', '预约上门取件'],
    medium: ['已下单', '订单生成', '等待取件', '预约取件', '订单受理', '订单处理中', '等待快递员', '等待上门', '预约上门', '准备取件', '安排取件'],
    low: ['订单信息', '寄件信息', '下单成功', '订单确认', '等待处理', '信息录入', '订单创建']
  },

  [LOGISTICS_STATUS.PICKED_UP]: {
    high: ['已揽收', '揽件成功', '收件成功', '已收件', '揽投员已收件', '快递员已取件', '揽件', '已揽件', '取件成功', '上门收件', '收寄成功', '成功揽收', '揽收完成'],
    medium: ['已取件', '已从.*收寄', '收寄完成', '揽投员', '已收取', '收件完成', '快递员收件', '业务员收件', '已收货', '收件员', '取件员', '收件人员', '揽收人员'],
    low: ['已从', '收寄', '取件', '收货', '收取', '揽收网点', '收件网点', '收件点', '取件点']
  },

  [LOGISTICS_STATUS.IN_TRANSIT]: {
    high: ['运输中', '在途中', '运输途中', '正在运输', '已发往.*中转', '离开.*中转', '运输', '在途', '途中', '运送中', '转运途中', '正在转运', '运输过程中'],
    medium: ['转运中', '中转', '发往', '离开', '到达.*中转', '经过', '途经', '装车发往', '已装车', '发车', '运往', '送往', '转运', '中转处理', '分拣中', '正在分拣'],
    low: ['中转站', '分拣中心', '装车', '发车', '运输', '转运', '分拣', '集散', '处理中心', '转运中心', '集散中心', '分拣完成', '装卸', '中转处理']
  },

  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: {
    high: ['派送中', '正在派送', '派件中', '配送中', '正在配送', '开始派送', '派送员.*派送', '出库派送', '安排派送', '派送', '配送', '正在投递', '投递中'],
    medium: ['投递中', '快递员.*派送', '派送员', '配送员', '正在派件', '派送途中', '准备派送', '派件', '投递', '送件中', '配送途中', '正在投递', '派送人员', '投递人员'],
    low: ['最后一公里', '同城配送', '本地派送', '终端派送', '末端配送', '派送站', '配送站', '投递员', '派送网点', '配送网点']
  },

  [LOGISTICS_STATUS.DELIVERED]: {
    high: ['已签收', '签收成功', '投递成功', '派送成功', '妥投', '本人签收', '签收', '收货签收', '客户签收', '成功签收', '完成签收', '签收完成', '投递完成'],
    medium: ['配送完成', '已投递', '代收', '他人代收', '成功投递', '投递完成', '签收人', '代签收', '家人代收', '同事代收', '邻居代收', '代为签收', '委托签收'],
    low: ['收件人', '快递柜', '驿站', '代签', '菜鸟驿站', '丰巢', '速递易', '近邻宝', '收件', '取件', '自提', '智能柜', '自助柜']
  },

  [LOGISTICS_STATUS.FAILED_DELIVERY]: {
    high: ['派送失败', '投递失败', '配送失败', '无人签收', '拒收', '客户拒收', '派送不成功', '投递不成功', '无法派送', '派送异常', '投递异常', '配送异常'],
    medium: ['地址错误', '联系不上', '电话无人接听', '改派', '延误派送', '无法联系', '地址不详', '收件人不在', '无人接听', '联系失败', '改期配送', '地址有误', '信息有误'],
    low: ['暂存', '改期派送', '预约派送', '地址有误', '电话错误', '信息不全', '待联系', '重新派送', '二次派送', '改期投递']
  },

  [LOGISTICS_STATUS.RETURNED]: {
    high: ['已退回', '退回发件人', '原路返回', '逆向物流', '退件', '退回', '返回发件人', '退货', '原件退回', '退回寄件人', '退回原地'],
    medium: ['退回', '返回', '退货', '回退', '退回寄件人', '返回原地', '逆向运输', '退回处理', '原路退回', '逆向配送'],
    low: ['返回途中', '退回中', '退回运输', '逆向', '回程', '退回流程', '逆向流程']
  },

  [LOGISTICS_STATUS.EXCEPTION]: {
    high: ['异常', '问题件', '滞留', '丢失', '破损', '包裹异常', '运输异常', '快件异常', '处理异常', '派送异常', '投递异常', '系统异常', '网络异常'],
    medium: ['超时', '延误', '无法派送', '地址不详', '收件人信息有误', '信息错误', '联系异常', '处理失败', '系统异常', '网络异常', '服务异常', '操作异常'],
    low: ['暂扣', '待处理', '需核实', '待确认', '核实中', '查询中', '调查中', '待查', '核查中']
  }
}

/**
 * 分析物流状态
 * @param {Array} orderTrack - 物流轨迹数组，按时间倒序排列
 * @returns {Object} 分析结果
 */
export function analyzeLogisticsStatus(orderTrack) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      latestTrack: null,
      confidence: 0,
      analysisMethod: 'default'
    }
  }

  // 获取最新的轨迹记录
  const latestTrack = orderTrack[0]
  const context = latestTrack.context || latestTrack.content || ''

  // 优化策略：优先分析最新记录，如果置信度不够则分析整个轨迹
  const latestAnalysis = analyzeTrackContext(context, latestTrack)

  // 如果最新记录分析置信度足够高且不是模糊状态，直接返回结果
  if (latestAnalysis.confidence >= 0.8 && !isAmbiguousContext(context)) {
    return {
      ...latestAnalysis,
      latestTrack,
      analysisMethod: 'latest_record_high_confidence'
    }
  }

  // 如果最新记录置信度不够或者是模糊状态，进行全轨迹分析
  const fullTrackAnalysis = analyzeFullTrackHistory(orderTrack)

  // 比较两种分析结果，选择更可靠的
  const finalResult = selectBestAnalysis(latestAnalysis, fullTrackAnalysis)

  return {
    ...finalResult,
    latestTrack,
    latestAnalysis,
    fullTrackAnalysis
  }
}

/**
 * 分析单条轨迹上下文
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 完整的轨迹记录（包含时间等信息）
 * @returns {Object} 分析结果
 */
function analyzeTrackContext(context, trackRecord = {}) {
  let maxScore = 0
  let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP
  let statusScores = {}

  // 遍历所有状态的关键词
  for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
    const score = calculateKeywordScore(context, keywords)
    statusScores[status] = score

    if (score > maxScore) {
      maxScore = score
      detectedStatus = status
    }
  }

  // 先保存原始关键词匹配结果
  const keywordMatchResult = {
    status: detectedStatus,
    score: maxScore,
    confidence: Math.min(maxScore / 50, 1)
  }

  // 语义增强分析
  const semanticResult = enhancedSemanticAnalysis(context)

  // 时间因素分析
  const timeAnalysis = analyzeTimeFactors(trackRecord)

  // 语义分析加成
  maxScore += semanticResult.semanticScore

  // 时间因素加成
  maxScore += timeAnalysis.timeBonus

  // 使用 compromise 分析结果进行状态修正
  const compromiseStatus = predictStatusFromCompromise(semanticResult.compromiseAnalysis, context)
  if (compromiseStatus && compromiseStatus.confidence > 0.7) {
    // 如果关键词匹配得分很高（超过25分），优先相信关键词匹配
    if (keywordMatchResult.score >= 25) {
      maxScore += 5
    } else {
      // 关键词匹配得分不高时，考虑 compromise 建议
      if (compromiseStatus.status !== detectedStatus) {
        const currentConfidence = Math.min(maxScore / 50, 1)
        if (compromiseStatus.confidence > currentConfidence) {
          detectedStatus = compromiseStatus.status
          maxScore = compromiseStatus.confidence * 50
        }
      } else {
        // 状态一致时，提升置信度
        maxScore += 8
      }
    }
  }

  // 计算最终置信度
  let confidence = Math.min(maxScore / 50, 1)
  confidence = Math.max(confidence, 0.1)

  return {
    status: detectedStatus,
    statusText: STATUS_TEXT_MAP[detectedStatus],
    confidence,
    rawScore: maxScore,
    keywordScores: statusScores,
    semanticAnalysis: semanticResult,
    timeAnalysis,
    analysisMethod: 'single_track'
  }
}

/**
 * 分析完整物流轨迹历史
 * @param {Array} orderTrack - 物流轨迹数组
 * @returns {Object} 分析结果
 */
function analyzeFullTrackHistory(orderTrack) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0,
      analysisMethod: 'full_track_empty'
    }
  }

  // 分析状态转换序列
  const statusSequence = []
  const trackAnalyses = []

  // 分析每条轨迹记录
  for (let i = 0; i < Math.min(orderTrack.length, 5); i++) {
    const track = orderTrack[i]
    const context = track.context || track.content || ''
    const analysis = analyzeTrackContext(context, track)

    trackAnalyses.push({
      ...analysis,
      trackIndex: i,
      timestamp: track.msgTime || track.time,
      isLatest: i === 0
    })

    statusSequence.push(analysis.status)
  }

  // 状态一致性检查
  const consistencyAnalysis = analyzeStatusConsistency(statusSequence)

  // 状态转换逻辑验证
  const transitionAnalysis = validateStatusTransitions(statusSequence)

  // 综合分析得出最终状态
  const finalStatus = determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis)

  // 如果最新记录很模糊，尝试从历史轨迹推断当前状态
  if (trackAnalyses.length > 1 && trackAnalyses[0].confidence < 0.5) {
    const inferredStatus = inferStatusFromHistory(trackAnalyses)
    if (inferredStatus && inferredStatus.confidence > finalStatus.confidence) {
      return {
        ...inferredStatus,
        statusSequence,
        trackAnalyses,
        consistencyAnalysis,
        transitionAnalysis,
        analysisMethod: 'inferred_from_history'
      }
    }
  }

  return {
    ...finalStatus,
    statusSequence,
    trackAnalyses,
    consistencyAnalysis,
    transitionAnalysis,
    analysisMethod: 'full_track_analysis'
  }
}

/**
 * 选择最佳分析结果
 * @param {Object} latestAnalysis - 最新记录分析结果
 * @param {Object} fullTrackAnalysis - 全轨迹分析结果
 * @returns {Object} 最终分析结果
 */
function selectBestAnalysis(latestAnalysis, fullTrackAnalysis) {
  // 如果最新记录置信度很高且是终态，优先选择
  if (latestAnalysis.confidence >= 0.8 && isFinalStatus(latestAnalysis.status)) {
    return {
      ...latestAnalysis,
      analysisMethod: 'latest_high_confidence_final'
    }
  }

  // 如果全轨迹分析置信度更高，选择全轨迹结果
  if (fullTrackAnalysis.confidence > latestAnalysis.confidence + 0.2) {
    return {
      ...fullTrackAnalysis,
      analysisMethod: 'full_track_higher_confidence'
    }
  }

  // 如果状态一致且都有合理置信度，选择置信度更高的
  if (latestAnalysis.status === fullTrackAnalysis.status) {
    const bestAnalysis = latestAnalysis.confidence >= fullTrackAnalysis.confidence
      ? latestAnalysis
      : fullTrackAnalysis

    return {
      ...bestAnalysis,
      confidence: Math.max(latestAnalysis.confidence, fullTrackAnalysis.confidence),
      analysisMethod: 'consistent_status_best_confidence'
    }
  }

  // 默认选择最新记录分析，但降低置信度
  return {
    ...latestAnalysis,
    confidence: Math.max(latestAnalysis.confidence * 0.8, 0.1),
    analysisMethod: 'latest_with_reduced_confidence'
  }
}

/**
 * 基于 compromise 分析结果预测物流状态
 * @param {Object} compromiseAnalysis - compromise 分析结果
 * @param {string} context - 原始上下文
 * @returns {Object|null} 预测结果
 */
function predictStatusFromCompromise(compromiseAnalysis, context) {
  if (!compromiseAnalysis || compromiseAnalysis.semanticBonus === 0) {
    return null
  }

  const { verbs, tenseAnalysis, sentiment, places } = compromiseAnalysis
  let predictedStatus = null
  let confidence = 0.5

  // 基于动词预测状态
  const verbLower = verbs.map(v => v.toLowerCase())

  if (verbLower.some(v => ['delivered', 'signed', 'completed', 'received'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.DELIVERED
    confidence = 0.9
  } else if (verbLower.some(v => ['delivering', 'dispatching', 'sending'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.8
  } else if (verbLower.some(v => ['picked', 'collected', 'received'].includes(v)) && tenseAnalysis.isPast) {
    predictedStatus = LOGISTICS_STATUS.PICKED_UP
    confidence = 0.8
  } else if (verbLower.some(v => ['moving', 'traveling', 'transporting'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
    confidence = 0.7
  } else if (verbLower.some(v => ['failed', 'rejected', 'unable'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    confidence = 0.8
  }

  // 基于时态调整预测
  if (tenseAnalysis.isPast && sentiment.polarity > 0) {
    // 过去时态 + 正面情感 = 可能已完成
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.DELIVERED
      confidence = 0.6
    } else if (predictedStatus === LOGISTICS_STATUS.DELIVERED) {
      confidence = Math.min(confidence + 0.1, 1)
    }
  } else if (tenseAnalysis.isProgressive) {
    // 进行时态 = 正在进行的动作
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
      confidence = 0.6
    }
  }

  // 基于地点信息调整
  if (places.length > 0) {
    confidence = Math.min(confidence + 0.1, 1)
  }

  // 基于情感极性调整
  if (sentiment.polarity < -1) {
    // 强烈负面情感可能表示问题
    if (predictedStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
      predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    } else if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.EXCEPTION
      confidence = 0.7
    }
  }

  return predictedStatus ? {
    status: predictedStatus,
    confidence,
    source: 'compromise'
  } : null
}

/**
 * 计算关键词匹配分数 - 支持权重分级和智能匹配
 * @param {string} text - 待分析文本
 * @param {Object} keywords - 分级关键词对象 {high: [], medium: [], low: []}
 * @returns {number} 匹配分数
 */
function calculateKeywordScore(text, keywords) {
  let score = 0
  const lowerText = text.toLowerCase()
  const textLength = text.length

  // 动态权重配置 - 根据文本长度调整
  const baseWeights = { high: 30, medium: 15, low: 8 }
  const lengthFactor = Math.min(textLength / 20, 2) // 文本越长，权重略微降低
  const weights = {
    high: Math.round(baseWeights.high / lengthFactor),
    medium: Math.round(baseWeights.medium / lengthFactor),
    low: Math.round(baseWeights.low / lengthFactor)
  }

  for (const [level, keywordList] of Object.entries(keywords)) {
    const weight = weights[level] || 8

    for (const keyword of keywordList) {
      const lowerKeyword = keyword.toLowerCase()
      let matchScore = 0

      // 支持正则表达式匹配
      if (keyword.includes('.*')) {
        try {
          const regex = new RegExp(lowerKeyword, 'i')
          if (regex.test(lowerText)) {
            matchScore = weight * 1.5 // 正则匹配额外加分
          }
        } catch (e) {
          // 正则表达式错误时降级为普通匹配
          const fallbackKeyword = lowerKeyword.replace(/\.\*/g, '')
          if (lowerText.includes(fallbackKeyword)) {
            matchScore = weight
          }
        }
      } else {
        // 智能关键词匹配
        if (lowerText.includes(lowerKeyword)) {
          // 计算匹配质量
          const matchQuality = calculateMatchQuality(lowerText, lowerKeyword)
          matchScore = weight * matchQuality
        }
      }

      // 避免重复计分（同一个关键词在文本中多次出现）
      if (matchScore > 0) {
        const occurrences = (lowerText.match(new RegExp(lowerKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length
        const occurrenceBonus = Math.min(occurrences * 0.2, 1) // 最多20%的额外加分
        score += matchScore * (1 + occurrenceBonus)
      }
    }
  }

  return Math.round(score)
}

/**
 * 计算关键词匹配质量
 * @param {string} text - 文本
 * @param {string} keyword - 关键词
 * @returns {number} 匹配质量系数 (0.5-2.0)
 */
function calculateMatchQuality(text, keyword) {
  // 完全匹配
  if (text.trim() === keyword) {
    return 2.0
  }

  // 独立词匹配（前后有空格或标点）
  const wordBoundaryRegex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i')
  if (wordBoundaryRegex.test(text)) {
    return 1.5
  }

  // 开头匹配
  if (text.startsWith(keyword)) {
    return 1.3
  }

  // 结尾匹配
  if (text.endsWith(keyword)) {
    return 1.2
  }

  // 普通包含匹配
  return 1.0
}

/**
 * 检测是否为模糊上下文
 * @param {string} context - 轨迹描述
 * @returns {boolean} 是否为模糊上下文
 */
function isAmbiguousContext(context) {
  const ambiguousKeywords = [
    '处理中', '处理', '操作中', '进行中', '执行中',
    '快件', '包裹', '订单', '货物', '商品',
    '正在', '进行', '执行', '操作'
  ]

  const lowerContext = context.toLowerCase()

  // 如果文本很短且只包含模糊词汇，认为是模糊的
  if (context.length < 10) {
    return ambiguousKeywords.some(keyword => lowerContext.includes(keyword))
  }

  // 如果文本中模糊词汇占比过高，认为是模糊的
  const ambiguousCount = ambiguousKeywords.filter(keyword =>
    lowerContext.includes(keyword)
  ).length

  return ambiguousCount >= 2 && context.length < 20
}

/**
 * 从历史轨迹推断当前状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @returns {Object|null} 推断结果
 */
function inferStatusFromHistory(trackAnalyses) {
  if (trackAnalyses.length < 2) return null

  // 获取最近几条有意义的轨迹
  const meaningfulTracks = trackAnalyses.filter(track => track.confidence > 0.3)

  if (meaningfulTracks.length === 0) return null

  // 找到最近的高置信度状态
  const lastHighConfidenceTrack = meaningfulTracks.find(track => track.confidence >= 0.7)

  if (!lastHighConfidenceTrack) return null

  // 基于最后的高置信度状态推断当前可能的状态
  const lastStatus = lastHighConfidenceTrack.status
  const possibleNextStates = getPossibleNextStates(lastStatus)

  // 如果只有一个可能的下一状态，使用它
  if (possibleNextStates.length === 1) {
    return {
      status: possibleNextStates[0],
      statusText: STATUS_TEXT_MAP[possibleNextStates[0]],
      confidence: Math.min(lastHighConfidenceTrack.confidence * 0.8, 0.7)
    }
  }

  // 如果有多个可能状态，选择最合理的
  const mostLikelyStatus = selectMostLikelyNextStatus(lastStatus, trackAnalyses)

  return mostLikelyStatus ? {
    status: mostLikelyStatus,
    statusText: STATUS_TEXT_MAP[mostLikelyStatus],
    confidence: Math.min(lastHighConfidenceTrack.confidence * 0.7, 0.6)
  } : null
}

/**
 * 获取可能的下一状态
 * @param {string} currentStatus - 当前状态
 * @returns {Array} 可能的下一状态数组
 */
function getPossibleNextStates(currentStatus) {
  const nextStatesMap = {
    [LOGISTICS_STATUS.PENDING_PICKUP]: [LOGISTICS_STATUS.PICKED_UP],
    [LOGISTICS_STATUS.PICKED_UP]: [LOGISTICS_STATUS.IN_TRANSIT],
    [LOGISTICS_STATUS.IN_TRANSIT]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY],
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: [LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.FAILED_DELIVERY],
    [LOGISTICS_STATUS.FAILED_DELIVERY]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED],
    [LOGISTICS_STATUS.DELIVERED]: [], // 终态
    [LOGISTICS_STATUS.RETURNED]: [], // 终态
    [LOGISTICS_STATUS.EXCEPTION]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY]
  }

  return nextStatesMap[currentStatus] || []
}

/**
 * 选择最可能的下一状态
 * @param {string} lastStatus - 最后的明确状态
 * @param {Array} trackAnalyses - 轨迹分析结果
 * @returns {string|null} 最可能的状态
 */
function selectMostLikelyNextStatus(lastStatus, trackAnalyses) {
  const possibleStates = getPossibleNextStates(lastStatus)

  if (possibleStates.length === 0) return lastStatus
  if (possibleStates.length === 1) return possibleStates[0]

  // 基于时间和常见流程选择
  if (lastStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
    // 派送中状态更可能变成已签收而不是失败
    return LOGISTICS_STATUS.DELIVERED
  }

  if (lastStatus === LOGISTICS_STATUS.FAILED_DELIVERY) {
    // 失败后更可能重新派送
    return LOGISTICS_STATUS.OUT_FOR_DELIVERY
  }

  // 默认返回第一个可能状态
  return possibleStates[0]
}

/**
 * 上下文语义增强分析 - 集成 compromise 库
 * @param {string} context - 轨迹描述
 * @returns {Object} 语义分析结果
 */
function enhancedSemanticAnalysis(context) {
  // 基础模式匹配
  const negativePatterns = [
    '未.*成功', '尚未.*', '暂未.*', '等待.*', '准备.*', '即将.*'
  ]

  const positivePatterns = [
    '已.*完成', '成功.*', '顺利.*', '正常.*'
  ]

  // 新增：语义强度分析
  const strongPositive = ['妥投', '签收成功', '投递完成']
  const strongNegative = ['派送失败', '拒收', '异常', '丢失']

  let semanticScore = 0
  const lowerContext = context.toLowerCase()

  // 使用 compromise 进行语义分析
  const compromiseResult = analyzeWithCompromise(context)

  // 检查强烈肯定词汇
  for (const pattern of strongPositive) {
    if (context.includes(pattern)) {
      semanticScore += 10
    }
  }

  // 检查强烈否定词汇
  for (const pattern of strongNegative) {
    if (context.includes(pattern)) {
      semanticScore -= 10
    }
  }

  // 检查否定词汇
  for (const pattern of negativePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore -= 5
    }
  }

  // 检查肯定词汇
  for (const pattern of positivePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore += 5
    }
  }

  // 融合 compromise 分析结果
  semanticScore += compromiseResult.semanticBonus

  // 新增：上下文连贯性分析
  const contextCoherence = analyzeContextCoherence(context)
  semanticScore += contextCoherence

  return {
    semanticScore,
    coherence: contextCoherence,
    intensity: Math.abs(semanticScore) > 8 ? 'high' : 'normal',
    compromiseAnalysis: compromiseResult
  }
}

/**
 * 使用 compromise 库进行语义分析
 * @param {string} context - 轨迹描述
 * @returns {Object} compromise 分析结果
 */
function analyzeWithCompromise(context) {
  try {
    const doc = nlp(context)

    // 提取动词和时态信息
    const verbs = doc.verbs().out('array')
    const pastTense = doc.verbs().toPastTense().out('array')
    const presentTense = doc.verbs().toPresentTense().out('array')

    // 提取名词（地点、人员等）
    const nouns = doc.nouns().out('array')
    const places = doc.places().out('array')
    const people = doc.people().out('array')

    // 情感分析
    const sentiment = analyzeSentimentWithCompromise(doc)

    // 时态分析
    const tenseAnalysis = analyzeTensePattern(doc)

    // 计算语义加成分数
    let semanticBonus = 0

    // 动词完成度分析
    if (verbs.some(verb => ['delivered', 'completed', 'finished', 'signed'].includes(verb.toLowerCase()))) {
      semanticBonus += 8
    }

    // 进行时态加成
    if (tenseAnalysis.isProgressive) {
      semanticBonus += 5
    }

    // 过去时态加成（表示已完成）
    if (tenseAnalysis.isPast) {
      semanticBonus += 6
    }

    // 地点信息加成
    if (places.length > 0) {
      semanticBonus += 3
    }

    // 人员信息加成
    if (people.length > 0) {
      semanticBonus += 2
    }

    // 情感极性加成
    semanticBonus += sentiment.polarity * 3

    return {
      verbs,
      nouns,
      places,
      people,
      sentiment,
      tenseAnalysis,
      semanticBonus: Math.max(-10, Math.min(10, semanticBonus)) // 限制范围
    }
  } catch (error) {
    console.warn('Compromise analysis failed:', error)
    return {
      verbs: [],
      nouns: [],
      places: [],
      people: [],
      sentiment: { polarity: 0, confidence: 0 },
      tenseAnalysis: { isPast: false, isProgressive: false },
      semanticBonus: 0
    }
  }
}

/**
 * 使用 compromise 进行情感分析
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 情感分析结果
 */
function analyzeSentimentWithCompromise(doc) {
  // 提取形容词进行情感分析
  const adjectives = doc.adjectives().out('array')

  // 定义情感词汇
  const positiveWords = ['successful', 'completed', 'delivered', 'good', 'normal', '成功', '完成', '正常', '顺利']
  const negativeWords = ['failed', 'error', 'problem', 'delayed', 'unable', '失败', '错误', '延误', '异常']

  let positiveScore = 0
  let negativeScore = 0

  // 分析形容词情感倾向
  adjectives.forEach(adj => {
    const lowerAdj = adj.toLowerCase()
    if (positiveWords.some(word => lowerAdj.includes(word))) {
      positiveScore++
    }
    if (negativeWords.some(word => lowerAdj.includes(word))) {
      negativeScore++
    }
  })

  // 分析整体文本情感
  const text = doc.out('text').toLowerCase()
  positiveWords.forEach(word => {
    if (text.includes(word)) positiveScore++
  })
  negativeWords.forEach(word => {
    if (text.includes(word)) negativeScore++
  })

  const polarity = positiveScore - negativeScore
  const confidence = Math.abs(polarity) / Math.max(positiveScore + negativeScore, 1)

  return {
    positive: positiveScore,
    negative: negativeScore,
    polarity,
    confidence
  }
}

/**
 * 分析时态模式
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 时态分析结果
 */
function analyzeTensePattern(doc) {
  const verbs = doc.verbs()

  // 检查是否有过去时态
  const pastVerbs = verbs.toPastTense().out('array')
  const isPast = pastVerbs.length > 0

  // 检查是否有进行时态
  const progressiveVerbs = verbs.toGerund().out('array')
  const isProgressive = progressiveVerbs.length > 0 || doc.has('#Gerund')

  // 检查是否有完成时态
  const perfectVerbs = verbs.conjugate().map(v => v.PastTense).filter(Boolean)
  const isPerfect = perfectVerbs.length > 0

  return {
    isPast,
    isProgressive,
    isPerfect,
    dominantTense: isPast ? 'past' : isProgressive ? 'progressive' : 'present'
  }
}

/**
 * 分析时间因素
 * @param {Object} trackRecord - 轨迹记录
 * @returns {Object} 时间分析结果
 */
function analyzeTimeFactors(trackRecord) {
  let timeBonus = 0
  const timestamp = trackRecord.msgTime || trackRecord.time

  if (!timestamp) {
    return { timeBonus: 0, hasValidTime: false }
  }

  try {
    const trackTime = new Date(timestamp)
    const now = new Date()
    const timeDiff = now - trackTime
    const hoursDiff = timeDiff / (1000 * 60 * 60)

    // 最新记录时间越近，置信度越高
    if (hoursDiff <= 2) {
      timeBonus += 5 // 2小时内的记录
    } else if (hoursDiff <= 24) {
      timeBonus += 3 // 24小时内的记录
    } else if (hoursDiff <= 72) {
      timeBonus += 1 // 3天内的记录
    }

    return {
      timeBonus,
      hasValidTime: true,
      hoursSinceUpdate: hoursDiff,
      isRecent: hoursDiff <= 24
    }
  } catch (error) {
    return { timeBonus: 0, hasValidTime: false }
  }
}

/**
 * 分析状态一致性
 * @param {Array} statusSequence - 状态序列
 * @returns {Object} 一致性分析结果
 */
function analyzeStatusConsistency(statusSequence) {
  if (statusSequence.length <= 1) {
    return { isConsistent: true, confidence: 1, dominantStatus: statusSequence[0] }
  }

  // 统计各状态出现频率
  const statusCount = {}
  statusSequence.forEach(status => {
    statusCount[status] = (statusCount[status] || 0) + 1
  })

  const totalCount = statusSequence.length
  const maxCount = Math.max(...Object.values(statusCount))
  const dominantStatus = Object.keys(statusCount).find(status => statusCount[status] === maxCount)

  const consistency = maxCount / totalCount

  return {
    isConsistent: consistency >= 0.6,
    confidence: consistency,
    dominantStatus,
    statusDistribution: statusCount
  }
}

/**
 * 验证状态转换逻辑
 * @param {Array} statusSequence - 状态序列（按时间倒序）
 * @returns {Object} 转换验证结果
 */
function validateStatusTransitions(statusSequence) {
  if (statusSequence.length <= 1) {
    return { isValid: true, confidence: 1, invalidTransitions: [] }
  }

  // 定义合理的状态转换规则
  const validTransitions = {
    [LOGISTICS_STATUS.PENDING_PICKUP]: [LOGISTICS_STATUS.PICKED_UP, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.PICKED_UP]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.IN_TRANSIT]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: [LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.FAILED_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.DELIVERED]: [], // 终态
    [LOGISTICS_STATUS.FAILED_DELIVERY]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.RETURNED]: [], // 终态
    [LOGISTICS_STATUS.EXCEPTION]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED]
  }

  const invalidTransitions = []
  let validTransitionCount = 0

  // 检查相邻状态转换的合理性（注意：statusSequence是倒序的）
  for (let i = 0; i < statusSequence.length - 1; i++) {
    const currentStatus = statusSequence[i] // 较新的状态
    const previousStatus = statusSequence[i + 1] // 较旧的状态

    const allowedNextStates = validTransitions[previousStatus] || []

    if (allowedNextStates.includes(currentStatus) || currentStatus === previousStatus) {
      validTransitionCount++
    } else {
      invalidTransitions.push({
        from: previousStatus,
        to: currentStatus,
        index: i
      })
    }
  }

  const totalTransitions = statusSequence.length - 1
  const validityRatio = totalTransitions > 0 ? validTransitionCount / totalTransitions : 1

  return {
    isValid: validityRatio >= 0.7,
    confidence: validityRatio,
    invalidTransitions,
    validTransitionCount,
    totalTransitions
  }
}

/**
 * 分析上下文连贯性
 * @param {string} context - 轨迹描述
 * @returns {number} 连贯性分数
 */
function analyzeContextCoherence(context) {
  let coherenceScore = 0

  // 检查时间逻辑词
  const timeLogicWords = ['然后', '接着', '随后', '之后', '现在', '目前']
  timeLogicWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  // 检查因果关系词
  const causalWords = ['因为', '由于', '所以', '因此', '导致']
  causalWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 3
  })

  // 检查地点连续性
  const locationWords = ['从', '到', '经过', '途径', '抵达']
  locationWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  return Math.min(coherenceScore, 5) // 限制最大加分
}

/**
 * 确定最终状态
 * @param {Array} trackAnalyses - 轨迹分析结果数组
 * @param {Object} consistencyAnalysis - 一致性分析结果
 * @param {Object} transitionAnalysis - 转换分析结果
 * @returns {Object} 最终状态结果
 */
function determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis) {
  if (trackAnalyses.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0
    }
  }

  // 获取最新记录的分析结果
  const latestAnalysis = trackAnalyses[0]

  // 如果最新记录是终态且置信度高，直接返回
  if (isFinalStatus(latestAnalysis.status) && latestAnalysis.confidence >= 0.7) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: Math.min(latestAnalysis.confidence + 0.1, 1)
    }
  }

  // 如果状态转换逻辑有效且一致性好，使用主导状态
  if (transitionAnalysis.isValid && consistencyAnalysis.isConsistent) {
    const finalStatus = consistencyAnalysis.dominantStatus
    const baseConfidence = Math.max(
      latestAnalysis.confidence,
      consistencyAnalysis.confidence * 0.8
    )

    return {
      status: finalStatus,
      statusText: STATUS_TEXT_MAP[finalStatus],
      confidence: Math.min(baseConfidence + 0.15, 1)
    }
  }

  // 如果转换逻辑无效，但最新记录置信度可接受，使用最新记录
  if (latestAnalysis.confidence >= 0.5) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: latestAnalysis.confidence * 0.9 // 略微降低置信度
    }
  }

  // 最后的兜底策略：使用主导状态但降低置信度
  const fallbackStatus = consistencyAnalysis.dominantStatus || LOGISTICS_STATUS.PENDING_PICKUP
  return {
    status: fallbackStatus,
    statusText: STATUS_TEXT_MAP[fallbackStatus],
    confidence: Math.max(consistencyAnalysis.confidence * 0.6, 0.1)
  }
}

/**
 * 判断是否为终态状态
 * @param {string} status - 物流状态
 * @returns {boolean} 是否为终态
 */
export function isFinalStatus(status) {
  return [
    LOGISTICS_STATUS.DELIVERED,
    LOGISTICS_STATUS.RETURNED,
    LOGISTICS_STATUS.EXCEPTION
  ].includes(status)
}

/**
 * 获取状态的紧急程度
 * @param {string} status - 物流状态
 * @returns {number} 紧急程度 (1-5, 5最紧急)
 */
export function getStatusUrgency(status) {
  const urgencyMap = {
    [LOGISTICS_STATUS.EXCEPTION]: 5,
    [LOGISTICS_STATUS.FAILED_DELIVERY]: 4,
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 3,
    [LOGISTICS_STATUS.IN_TRANSIT]: 2,
    [LOGISTICS_STATUS.PICKED_UP]: 2,
    [LOGISTICS_STATUS.PENDING_PICKUP]: 3,
    [LOGISTICS_STATUS.DELIVERED]: 1,
    [LOGISTICS_STATUS.RETURNED]: 4
  }

  return urgencyMap[status] || 2
}

/**
 * 获取详细的物流状态分析报告
 * @param {Array} orderTrack - 物流轨迹数组
 * @returns {Object} 详细分析报告
 */
export function getDetailedLogisticsAnalysis(orderTrack) {
  const basicAnalysis = analyzeLogisticsStatus(orderTrack)

  return {
    ...basicAnalysis,
    urgency: getStatusUrgency(basicAnalysis.status),
    isFinal: isFinalStatus(basicAnalysis.status),
    recommendations: generateRecommendations(basicAnalysis),
    trackCount: orderTrack?.length || 0,
    analysisTimestamp: new Date().toISOString()
  }
}

/**
 * 生成基于分析结果的建议
 * @param {Object} analysis - 分析结果
 * @returns {Array} 建议列表
 */
function generateRecommendations(analysis) {
  const recommendations = []

  if (analysis.confidence < 0.5) {
    recommendations.push('建议联系快递公司确认具体状态')
  }

  if (analysis.status === LOGISTICS_STATUS.FAILED_DELIVERY) {
    recommendations.push('建议主动联系收件人确认地址和联系方式')
  }

  if (analysis.status === LOGISTICS_STATUS.EXCEPTION) {
    recommendations.push('建议立即联系快递公司处理异常情况')
  }

  if (analysis.status === LOGISTICS_STATUS.OUT_FOR_DELIVERY && analysis.timeAnalysis?.hoursSinceUpdate > 24) {
    recommendations.push('派送时间较长，建议联系派送员确认情况')
  }

  return recommendations
}
