<template>
  <div class="address-form order-address-edit">
    <!-- 提示信息 -->
    <div class="tip-box">
      <div class="tip-content">
        提示：发货前订单仅支持修改一次，修改后可能会影响物流时效。若商品因换地址、已发货、运费变更等原因导致修改失败，请您谅解。
      </div>
    </div>

    <!-- 当前地址 -->
    <div class="address-info-section">
      <div class="section-title">原地址</div>
      <div class="address-info">
        <div class="address-info-left">
          <div class="contact">{{ originalAddressInfo.recName }} {{ originalAddressInfo.recPhone }}</div>
          <div class="edit-op" @click="editOriginalAddress">
            <div class="edit-btn">修改原地址</div>
            <img class="edit-icon" src="@/static/images/mod-address-icon.png" alt=""/>
          </div>
        </div>
        <div class="address-info-right">
          <div class="address">{{ originalAddressInfo.recAddress }}</div>
        </div>
      </div>
    </div>

    <!-- 选择新的收货地址 -->
    <div class="select-address-section">
      <div class="title-row">
        <div class="title">选择新收货地址</div>
        <div class="add-address" @click="addNewAddress">+ 新增地址</div>
      </div>
    </div>

    <div class="address-list-container" ref="addressListRef">
      <template v-if="addressList.length > 0">
        <div
          v-for="(item, index) in addressList"
          :key="item.addressId || index"
          class="address-item"
          :class="{ 'cur': selectedIndex === index }"
          @click="selectAddress(index)"
        >
          <div class="address-content">
            <div class="contact">{{ item.recName }} {{ item.recPhone }}</div>
            <div class="address">
              <span>{{ item.provinceName }}&nbsp;</span>
              <span>{{ item.cityName }}&nbsp;</span>
              <span>{{ item.countyName }}&nbsp;</span>
              <span>{{ item.townName }}&nbsp;</span>
              <span>{{ item.addrDetail }}</span>
            </div>
          </div>
          <div class="radio-btn">
            <img :src="selectedIndex === index ? checkImgSrc : uncheckImgSrc" alt="选择状态" />
          </div>
        </div>
      </template>
      <div v-else class="empty-address">暂无收货地址，请添加</div>
    </div>

    <!-- 确认修改按钮 -->
    <AddressActionBar>
      <WoButton
        type="primary"
        block
        size="xlarge"
        :disabled="isLoading || selectedIndex === '' || selectedIndex < 0 || selectedIndex >= addressList.length"
        @click="confirmOrderAddressChange"
      >
        {{ isLoading ? '提交中...' : '确认修改' }}
      </WoButton>
    </AddressActionBar>
  </div>



  <!-- 原地址编辑弹窗 -->
  <AddressQuickEditPopup
    v-model:visible="showLocationSelector"
    :initial-data="originalAddressFormData"
    @save="saveOriginalAddress"
    @validate="onFormValidate"
    @region-change="onRegionChange"
    ref="addressEditPopupRef"
  />
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressQuickEditPopup from '@components/Address/AddressQuickEditPopup.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'

import { modifyOrderAddress } from '@api/interface/order.js'
import { useUserStore } from '@/store/modules/user.js'

import checkImg from '@/static/images/wo-select.png'
import uncheckImg from '@/static/images/no-select.png'

// 路由实例
const router = useRouter()
const route = useRoute()

// Store
const userStore = useUserStore()

// 状态管理
const isFormValid = ref(false)
const originalAddressFormData = ref({})

// 订单地址编辑相关状态
const orderId = ref('')
const originalAddressInfo = ref({
  recName: '',
  recPhone: '',
  recAddress: ''
})
const selectedIndex = ref('')
const isLoading = ref(false)
const showLocationSelector = ref(false)

// 组件引用
const addressEditPopupRef = ref(null)

// 模板引用
const addressListRef = ref(null)

// 图片资源
const checkImgSrc = checkImg
const uncheckImgSrc = uncheckImg

// 计算属性

// 地址列表 (用于订单地址编辑模式)
const addressList = computed(() => userStore.addressList || [])

// 表单验证回调
const onFormValidate = ({ isValid }) => {
  isFormValid.value = isValid
}

// 地区变化回调
const onRegionChange = (regionData) => {
  // 可以在这里处理地区变化的逻辑
  console.log('地区变化:', regionData)
}







// 订单地址编辑相关方法
const initOrderAddressEditMode = () => {
  // 获取地址信息
  const addressInfoStr = route.query.addressInfo

  if (addressInfoStr) {
    try {
      originalAddressInfo.value = JSON.parse(decodeURIComponent(addressInfoStr))
    } catch (e) {
      console.error('解析地址信息失败', e)
      showToast('地址信息解析失败')
    }
  }
  // 获取订单ID
  orderId.value = route.query.orderId || ''
  if (!orderId.value) {
    console.error('缺少订单ID参数')
    showToast('缺少订单ID参数')
  }
}

const fetchAddressList = async () => {
  try {
    await userStore.queryAddrList({ force: true })
    nextTick(() => {
      scrollToSelectedAddress()
    })
  } catch (err) {
    console.error('获取地址列表失败', err)
    showToast('获取地址列表失败，请重试')
  }
}

const scrollToSelectedAddress = () => {
  const addressListEl = addressListRef.value
  if (!addressListEl) return

  const selectedEl = addressListEl.querySelector('.cur')
  if (selectedEl) {
    selectedEl.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

const selectAddress = (index) => {
  if (isLoading.value) return
  selectedIndex.value = index
}

const editOriginalAddress = () => {
  // 将原地址信息转换为 AddressForm 需要的格式
  originalAddressFormData.value = {
    ...originalAddressInfo.value,
    recName: originalAddressInfo.value.recName || '',
    recPhone: originalAddressInfo.value.recPhone || '',
    addrDetail: originalAddressInfo.value.recAddress || '',
  }
  showLocationSelector.value = true
}

const addNewAddress = () => {
  if (isLoading.value) return
  router.push({
    path: '/addr/add'
  })
}

const saveOriginalAddress = async (addressFormRef) => {
  if (isLoading.value) return

  // 验证表单
  if (!addressFormRef?.validateForm()) return

  isLoading.value = true
  showLoadingToast()

  try {
    // 获取表单数据
    const formData = addressFormRef.formData

    // 构建新的地址信息
    const newAddressInfo = {
      recName: formData.recName,
      recPhone: formData.recPhone,
      recAddress: formData.addrDetail
    }

    const orderAddressRecord = JSON.stringify({
      orderId: orderId.value,
      newAddress: newAddressInfo
    })

    const [err] = await modifyOrderAddress(orderAddressRecord)
    closeToast()

    if (err) {
      handleOrderAddressError(err)
    } else {
      // 更新原地址信息显示
      originalAddressInfo.value = newAddressInfo
      handleOrderAddressSuccess()
    }
  } catch (e) {
    console.error('修改地址异常', e)
    showToast('网络异常，请稍后重试')
  } finally {
    isLoading.value = false
    showLocationSelector.value = false
  }
}

const confirmOrderAddressChange = async () => {
  // 验证选择的地址
  if (selectedIndex.value === '' || selectedIndex.value < 0 || selectedIndex.value >= addressList.value.length) {
    showToast('请选择新收货地址')
    return
  }

  if (isLoading.value) return
  isLoading.value = true
  showLoadingToast()

  try {
    const selectedAddress = addressList.value[selectedIndex.value]
    const orderAddressRecord = JSON.stringify({
      orderId: orderId.value,
      newAddress: selectedAddress
    })

    const [err] = await modifyOrderAddress(orderAddressRecord)
    closeToast()

    if (err) {
      handleOrderAddressError(err)
    } else {
      handleOrderAddressSuccess()
    }
  } catch (e) {
    console.error('修改地址异常', e)
    showToast('网络异常，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

const handleOrderAddressError = (err) => {
  if (err.code === '9999') {
    showToast(err.msg || '地址修改失败')
  } else {
    // 需要用户确认的错误使用弹窗
    // 这里可以使用 vant 的 Dialog 组件
    showToast(err.msg || '地址修改失败')
  }
}

const handleOrderAddressSuccess = () => {
  showToast('修改信息已提交，修改成功后将更新订单信息。')
  setTimeout(() => {
    router.back()
  }, 1500)
}

onMounted(async () => {
  try {
    // 订单地址编辑模式
    initOrderAddressEditMode()
    await fetchAddressList()
  } catch (error) {
    console.error('页面初始化失败:', error)
    showToast('页面初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);

  // 订单地址编辑模式样式
  &.order-address-edit {
    background-color: @bg-color-gray;
    height: 100vh;
    padding-bottom: 50px;
    overflow: hidden;
  }

  &__header {
    padding: 24px 16px 16px;
    text-align: center;
    background: @bg-color-white;
    border-bottom: 1px solid #F0F2F5;
  }

  &__title {
    font-size: @font-size-20;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0 0 8px 0;
    letter-spacing: 0.3px;
  }

  &__subtitle {
    font-size: @font-size-13;
    color: @text-color-tertiary;
    margin: 0;
    letter-spacing: 0.1px;
  }

  &__content {
    flex: 1;
    padding: 10px;
    background-color: @bg-color-gray;
  }
}

.tip-box {
  padding: 9px 20px;
  background-color: #FFF5E6;

  .tip-content {
    color: #FF6E00;
    font-size: @font-size-11;
    line-height: 18px;
  }
}

.address-info-section {
  padding: 15px 20px;
  background-color: @bg-color-white;

  .section-title {
    font-size: @font-size-15;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin-bottom: 10px;
  }

  .address-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .address-info-left {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .contact {
        font-size: @font-size-14;
        color: @text-color-primary;
        margin-bottom: 5px;
      }

      .edit-op {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        font-size: @font-size-13;
        color: @text-color-secondary;

        .edit-icon {
          margin-left: 5px;
          width: 15px;
          height: 15px;
        }
      }
    }

    .address-info-right {
      .address {
        font-size: @font-size-13;
        color: @text-color-secondary;
        line-height: 18px;
        .multi-ellipsis(2);
      }
    }
  }
}

.address-list-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 20px;
  background-color: @bg-color-white;

  .empty-address {
    padding: 30px 0;
    text-align: center;
    color: @text-color-tertiary;
    font-size: @font-size-14;
  }

  .address-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid @divider-color-base;

    &:last-child {
      border-bottom: none;
    }

    &.cur {
      .radio-btn img {
        opacity: 1;
      }
    }

    .address-content {
      flex: 1;

      .contact {
        font-size: @font-size-13;
        color: @text-color-primary;
        margin-bottom: 5px;
      }

      .address {
        font-size: @font-size-13;
        color: @text-color-secondary;
        line-height: 18px;
      }
    }

    .radio-btn {
      margin-left: 10px;

      img {
        width: 18px;
        height: 18px;
        display: block;
      }
    }
  }
}

.select-address-section {
  margin-top: 10px;
  padding: 17px 20px;
  background-color: @bg-color-white;
  display: flex;
  flex-direction: column;

  .title-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      color: @text-color-primary;
    }

    .add-address {
      font-size: @font-size-14;
      color: @theme-color;
    }
  }
}
</style>
