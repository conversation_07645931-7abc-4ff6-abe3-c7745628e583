<template>
  <article class="after-sales-item">
    <WoCard>
      <header class="after-sales-item__header">
        <div class="after-sales-item__service-number">
          <span class="after-sales-item__service-text">
            服务单号：{{ orderData.afterSaleId || '暂无服务单号' }}
          </span>
          <img
            v-if="orderData.afterSaleId"
            src="@/static/images/copy.png"
            alt="复制"
            class="after-sales-item__copy-btn"
            loading="lazy"
            @click.stop="handleCopyOrderNumber(orderData.id)"
          />
        </div>
        <div v-if="orderData.applyType" class="after-sales-item__status">
          <i class="after-sales-status-icon" :class="afterSalesStatusClass"></i>
          <span class="after-sales-status-text">{{ afterSalesStatusText }}</span>
        </div>
      </header>
      <section class="after-sales-item__goods">
        <AfterSaleGoodsCard
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :show-actions="true"
          :item-id="orderData.id"
        >
          <template #tips>
            <p class="after-sales-tips" v-if="!isExpires && !afterSaleId && orderData.orderState !== '10'">
              该商品已超过售后期 <i class="after-sales-tips__icon" @click="showTipsDetail"></i>
            </p>
            <p class="after-sales-tips" v-else>{{ orderStateText }}</p>
          </template>
          <template #actions>
            <WoButton
              v-for="action in actionButtons"
              :key="action.key"
              :type="action.type || 'primary'"
              size="small"
              :disabled="action.disabled"
              @click.stop="action.handler"
            >
              {{ action.label }}
            </WoButton>
          </template>
        </AfterSaleGoodsCard>
      </section>
    </WoCard>

    <Popup
      class="after-sales-expiration-popup"
      safe-area-inset-bottom
      lock-scroll
      round
      position="bottom"
      v-model:show="afterSalesExpirationPopupShow"
    >
      <header class="after-sales-expiration-popup__header">
        <h3 class="after-sales-expiration-popup__title"></h3>
        <button class="after-sales-expiration-popup__close" @click="popupClose">
          <img src="../assets/popupClose.png" alt="关闭" />
        </button>
      </header>
      <main class="after-sales-expiration-popup__content">
        <div class="after-sales-expiration-popup__message">
          <p class="after-sales-expiration-popup__title-text">抱歉，订单已过售后申请时效</p>
          <p class="after-sales-expiration-popup__desc">商品已超过售后期限，如需售后可联系客服处理</p>
        </div>
      </main>
      <footer class="after-sales-expiration-popup__footer">
        <button class="after-sales-expiration-popup__confirm-btn" @click="afterSalesExpirationPopupShow = false">
          确定
        </button>
      </footer>
    </Popup>
  </article>
</template>

<script setup>
import { computed, toRefs, ref } from 'vue'
import { debounce } from 'lodash-es'
import useClipboard from 'vue-clipboard3'
import { showToast, Popup } from 'vant'
import dayjs from 'dayjs'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  },
  actionButtons: {
    type: Array,
    default: () => []
  }
})

const { orderData, actionButtons } = toRefs(props)
const { toClipboard } = useClipboard()

const afterSalesExpirationPopupShow = ref(false)

const afterSaleId = computed(() => orderData.value?.afterSaleId)

const isExpires = computed(() => {
  const expireTimeDateStr = orderData.value?.expireTime
  if (!expireTimeDateStr) return false
  return dayjs(expireTimeDateStr) > dayjs()
})

const orderStateText = computed(() => {
  const status = orderData.value?.orderState
  return status === '10' ? '订单已退款' : ''
})

const afterSalesStatusText = computed(() => {
  const statusMap = {
    '1': '退款',
    '2': '退货',
    '3': '换货',
    '4': '维修',
    '5': '补发商品'
  }
  return statusMap[orderData.value?.applyType] || ''
})

const afterSalesStatusClass = computed(() => {
  const classMap = {
    '1': 'after-sales-status-icon--refund',
    '2': 'after-sales-status-icon--return',
    '3': 'after-sales-status-icon--exchange',
    '4': 'after-sales-status-icon--maintenance',
    '5': 'after-sales-status-icon--reissue'
  }
  return classMap[orderData.value?.applyType] || ''
})

const handleCopyOrderNumber = debounce(async (orderNumber) => {
  try {
    await toClipboard(String(orderNumber))
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}, 300)

const showTipsDetail = () => {
  afterSalesExpirationPopupShow.value = true
}

const popupClose = () => {
  afterSalesExpirationPopupShow.value = false
}
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.after-sales-item {
  margin-bottom: @padding-page * 2;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @padding-page * 2;
  }

  &__service-number {
    display: flex;
    align-items: center;
    margin-right: @padding-button;
    width: 100%;
    overflow: hidden;
  }

  &__service-text {
    font-size: @font-size-11;
    color: @text-color-secondary;
    margin-right: 3px;
    .ellipsis();
  }

  &__copy-btn {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  &__goods {
    margin-bottom: @padding-page * 2;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.after-sales-status-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: @padding-page;
  background-repeat: no-repeat;
  background-size: contain;

  &--reissue {
    background-image: url("../assets/reissue-goods.png");
  }

  &--exchange {
    background-image: url("../assets/exchange-goods.png");
  }

  &--refund {
    background-image: url("../assets/refund-only.png");
  }

  &--return {
    background-image: url("../assets/return-of-goods.png");
  }

  &--maintenance {
    background-image: url("../assets/maintenance.png");
  }
}

.after-sales-status-text {
  font-size: @font-size-12;
  color: @text-color-tertiary;
  font-weight: @font-weight-400;
}

.after-sales-tips {
  display: flex;
  align-items: center;
  color: @text-color-tertiary;
  text-align: left;
  font-weight: @font-weight-400;
  font-size: @font-size-12;

  &__icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    background-image: url("../assets/question.png");
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
  }
}

.after-sales-expiration-popup {
  box-sizing: border-box;
  padding: @padding-button + 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 240px;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: @padding-button;
  }

  &__title {
    flex: 1;
    font-size: @font-size-17;
    color: @text-color-primary;
    text-align: center;
    line-height: 1;
    font-weight: @font-weight-400;
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;

    img {
      width: 14px;
      height: 14px;
    }
  }

  &__content {
    margin-bottom: 50px;
  }

  &__message {
    text-align: center;
  }

  &__title-text {
    font-size: @font-size-18 + 1px;
    color: @text-color-primary;
    font-weight: @font-weight-700;
    margin-bottom: @padding-button;
  }

  &__desc {
    margin-top: @padding-page * 2;
    font-size: @font-size-13;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
  }

  &__footer {
    width: 100%;
    height: 35px;
    margin-top: @padding-button + 5px;
  }

  &__confirm-btn {
    background: @gradient-orange-106;
    border-radius: @radius-9999;
    font-size: @font-size-17;
    color: @color-white;
    font-weight: @font-weight-400;
    width: 100%;
    height: 35px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
    border: none;
  }
}
</style>
