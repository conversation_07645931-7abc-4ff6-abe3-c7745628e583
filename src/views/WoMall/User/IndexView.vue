<template>
  <MainLayout>
    <div class="user-page">
      <header class="user-page__header">
        <h2 class="user-page__title">我的订单</h2>
        <nav class="order-status" role="navigation" aria-label="订单状态导航">
          <button v-for="item in bannerList" :key="item.key" class="order-status__item"
            :class="{ 'order-status__item--separator': item.separator }"
            :aria-label="item.separator ? '分隔符' : `${item.name}订单`" @click="handleOrderClick(item.key)">
            <!-- 分隔符 -->
            <i v-if="item.separator" :class="['order-status__separator', item.icon]" aria-hidden="true" />
            <!-- 订单状态 -->
            <div v-else class="order-status__content">
              <van-badge :content="orderCounts[item.key] || 0" :show-zero="false" :max="99" class="order-status__badge">
                <i :class="['order-status__icon', item.icon]" aria-hidden="true" />
              </van-badge>
              <span class="order-status__text">{{ item.name }}</span>
            </div>
          </button>
        </nav>
      </header>

      <div class="user-page__divider" />

      <section class="user-links">
        <button v-for="link in visibleLinks" :key="link.key" class="user-links__item" :aria-label="`进入${link.text}`"
          @click="link.handler">
          <span class="user-links__text">{{ link.text }}</span>
          <i class="user-links__arrow" aria-hidden="true" />
        </button>
      </section>
    </div>
  </MainLayout>
</template>

<script setup>
import { computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { throttle } from 'lodash-es'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from '@/store/modules/user'
import { useOrderStore } from '@/store/modules/order'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { closeToast, showLoadingToast } from 'vant'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const orderStore = useOrderStore()

// 订单状态列表 - 使用 shallowRef 避免深度响应
const bannerList = shallowRef([
  { name: '待付款', icon: 'icon-dfk', key: '0' },
  { name: '待发货', icon: 'icon-dfh', key: '3' },
  { name: '待收货', icon: 'icon-dsh', key: '5' },
  { name: '售后', icon: 'icon-sh', key: '-1' },
  { separator: true, icon: 'icon-separator', key: '999' },
  { name: '所有订单', icon: 'icon-sydd', key: '' }
])

// 缓存 bizCode 避免重复计算
const currentBizCode = getBizCode()
const isWishShow = ['ziying', 'labor', 'ygjd'].includes(currentBizCode)

// 订单数量缓存
const orderCounts = computed(() => {
  const counts = {}
  bannerList.value.forEach(item => {
    if (!item.separator) {
      counts[item.key] = orderStore.getCountByType(item.key)
    }
  })
  return counts
})

// 链接配置 - 预计算显示状态
const linkConfigs = shallowRef([
  {
    key: 'addr',
    text: '地址管理',
    handler: () => router.push({ path: '/addr/list', query: { callback: route.path } }),
    show: true
  },
  {
    key: 'afterSale',
    text: '售后服务',
    handler: () => router.push({
      path: '/wo-after-sales-list',
      query: { type: '-1', _t: Date.now().toString() }
    }),
    show: true
  },
  {
    key: 'help',
    text: '帮助中心',
    handler: () => router.push('/user/help'),
    show: currentBizCode === 'fupin'
  },
  {
    key: 'hotline',
    text: '在线客服',
    handler: () => {
      window.location.href = 'https://service.unicompayment.com/live800/chatClient/chatbox.jsp?companyID=9061&configID=47&pagereferrer=%e5%95%86%e5%9f%8e&chatfrom=sc&enterurl=sc&sc=sc'
    },
    show: true
  },
  {
    key: 'wish',
    text: '心愿单',
    handler: () => router.push({
      name: 'user-wish',
      query: { ...route.query, timestamp: Date.now() }
    }),
    show: isWishShow
  }
])

// 可见链接 - 预过滤
const visibleLinks = linkConfigs.value.filter(link => link.show)

// 节流处理订单点击
const handleOrderClick = throttle((type) => {
  if (type === '-1') {
    router.push({
      path: '/wo-after-sales',
      query: { type, _t: Date.now().toString() }
    })
  } else {
    router.push({
      path: '/user/order/list',
      query: { type, _t: Date.now().toString() }
    })
  }
}, 300)

// 检查登录并获取订单数据 - 优化加载策略
const checkLoginAndFetchData = async () => {
  try {
    await userStore.queryLoginStatus()
    if (userStore.isLogin) {
      showLoadingToast()
      await orderStore.fetchOrderCountDebounced()
      closeToast()
    }
  } catch (error) {
    // console.error('初始化数据失败:', error)
  }
}

onMounted(() => {
  checkLoginAndFetchData()
})

onUnmounted(() => {
  orderStore.clearCache()
})
</script>

<style lang="less" scoped>
.user-page {
  width: 100%;
  background: @bg-color-gray;
  contain: layout style;
  min-height: 100vh;

  &__header {
    padding: 20px 16px 18px;
    background: @bg-color-white;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    contain: layout;
    position: relative;
  }

  &__title {
    margin-bottom: 16px;
    text-align: left;
    font-size: @font-size-18;
    font-weight: @font-weight-700;
    color: @text-color-primary;
    contain: style;
    letter-spacing: 0.5px;
  }

  &__divider {
    width: 100%;
    height: 12px;
    background: @bg-color-gray;
    margin: 0;
    will-change: auto;
  }
}

.order-status {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: nowrap;
  text-align: center;
  contain: layout;
  gap: 8px;

  &__item {
    position: relative;
    width: 19%;
    border: none;
    background: transparent;
    padding: 12px 8px;
    cursor: pointer;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    border-radius: @radius-12;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &--separator {
      width: 5%;
      padding: 0;
    }

    // 悬停效果
    //&:hover:not(&--separator) {
    //  background: linear-gradient(135deg, rgba(255, 122, 10, 0.08) 0%, rgba(255, 122, 10, 0.04) 100%);
    //  transform: translateY(-2px);
    //  box-shadow: 0 4px 12px rgba(255, 122, 10, 0.15);
    //}

    // 添加点击反馈
    //&:active:not(&--separator) {
    //  transform: translateY(0) scale(0.96);
    //  transition: transform 0.1s ease;
    //  box-shadow: 0 2px 8px rgba(255, 122, 10, 0.2);
    //}
  }

  &__separator {
    display: block;
    margin: 0 auto;
    width: 2px;
    height: 40px;
    background: linear-gradient(180deg, transparent 0%, @divider-color-base 20%, @divider-color-base 80%, transparent 100%);
    border-radius: 1px;

    &.icon-separator {
      background-image: none;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    display: block;
    margin: 0 auto;
    width: 36px;
    height: 36px;
    background-size: 100%;
    background-repeat: no-repeat;
    will-change: transform;
    transform: translateZ(0);
    border-radius: @radius-8;
    padding: 6px;
    background-color: rgba(255, 122, 10, 0.08);
    transition: all 0.2s ease;

    &.icon-dfh {
      background-image: url(./assets/icon-status-dfh.png);
    }

    &.icon-dfk {
      background-image: url(./assets/icon-status-dfk.png);
    }

    &.icon-dsh {
      background-image: url(./assets/icon-status-dsh.png);
    }

    &.icon-ywc {
      background-image: url(./assets/icon-status-ywc.png);
    }

    &.icon-sh {
      background-image: url(./assets/icon-status-sh.png);
    }

    &.icon-sydd {
      background-image: url(./assets/icon-status-sydd.png);
    }
  }

  &__badge {
    position: relative;
    display: inline-block;

    :deep(.van-badge) {
      background: @theme-color;

      .van-badge__wrapper {
        position: static;
      }

      .van-badge__content {
        position: absolute;
        top: -8px;
        right: -8px;
        min-width: 18px;
        height: 18px;
        padding: 0 5px;
        background: linear-gradient(135deg, @color-red 0%, darken(@color-red, 10%) 100%);
        border: 2px solid @bg-color-white;
        border-radius: @radius-9999;
        font-size: @font-size-11;
        line-height: 14px;
        color: @text-color-white;
        text-align: center;
        box-shadow: 0 2px 8px rgba(255, 47, 47, 0.3);
        transform: scale(1);
        transform-origin: center;
        font-weight: @font-weight-600;
      }
    }
  }

  &__text {
    margin-top: 0;
    margin-bottom: 0;
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-500;
    letter-spacing: 0.2px;
  }
}

.user-links {
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  contain: layout; // 布局优化
  gap: 12px;

  &__item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 18px 20px;
    width: 100%;
    min-height: 60px;
    border: none;
    border-radius: @radius-12;
    background: @bg-color-white;
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // 添加微妙的渐变背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 122, 10, 0.02) 0%, rgba(255, 122, 10, 0.01) 100%);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    // 悬停效果
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);

      &::before {
        opacity: 1;
      }

      .user-links__arrow {
        transform: translateX(4px);
      }
    }

    //&:active {
    //  transform: translateY(0) scale(0.98);
    //  transition: transform 0.1s ease;
    //  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    //}
  }

  &__text {
    font-size: @font-size-16;
    color: @text-color-primary;
    contain: style; // 样式隔离
    font-weight: @font-weight-500;
    letter-spacing: 0.3px;
  }

  &__arrow {
    width: 20px;
    height: 20px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url(./assets/next-step.png);
    will-change: transform;
    transform: translateZ(0);
    transition: transform 0.2s ease;
    opacity: 0.6;
    filter: brightness(1.2);
  }
}

@media (max-width: 375px) {
  .user-page {
    &__header {
      padding: 16px 12px 14px;
    }

    &__title {
      font-size: @font-size-16;
    }
  }

  .order-status {
    gap: 4px;

    &__item {
      padding: 10px 6px;
    }

    &__icon {
      width: 32px;
      height: 32px;
    }

    &__text {
      font-size: @font-size-12;
    }
  }

  .user-links {
    padding: 12px;
    gap: 8px;

    &__item {
      padding: 16px 18px;
      min-height: 56px;
    }

    &__text {
      font-size: @font-size-15;
    }
  }
}
</style>
