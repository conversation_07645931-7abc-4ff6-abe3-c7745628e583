<template>
  <div class="goods-list-layout">
    <GoodsSkeletonLoader
      v-if="isLoading"
      :is-waterfall="isWaterfall"
      :skeleton-count="isWaterfall ? 4 : 3"
    />

    <section v-show="goodsList.length > 0 && !isWaterfall" class="list-layout">
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <ul class="goods-list-container">
          <ProductListItem
            v-for="(item, index) in goodsList"
            :key="`goods-${item.skuId || index}`"
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </ul>
      </van-list>
    </section>

    <section v-show="goodsList.length > 0 && isWaterfall" class="waterfall-layout">
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <Waterfall :list="goodsList" :breakpoints="breakpoints" :hasAroundGutter="false"
          :animation="false" :animationDuration="0" :horizontalOrder="true" :animationDelay="0">
          <template #default="{ item }">
            <GoodsWaterfallItem
              :item="item"
              @item-click="handleItemClick"
              @add-cart="handleAddCart"
            />
          </template>
        </Waterfall>
      </van-list>
    </section>

    <section v-if="goodsList.length <= 0 && !isLoading" class="empty-state">
      <WoEmpty :description="emptyDescription" />
    </section>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import GoodsSkeletonLoader from './GoodsSkeletonLoader.vue'
import ProductListItem from './ProductListItem.vue'
import GoodsWaterfallItem from './GoodsWaterfallItem.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  finished: {
    type: Boolean,
    default: false
  },
  isWaterfall: {
    type: Boolean,
    default: false
  },
  breakpoints: {
    type: Object,
    default: () => getDefaultBreakpoints()
  },
  emptyDescription: {
    type: String,
    default: '暂无商品'
  }
})

const {
  goodsList,
  isLoading,
  loading,
  finished,
  isWaterfall,
  breakpoints,
  emptyDescription
} = toRefs(props)

const emit = defineEmits(['load-more', 'item-click', 'add-cart', 'update:loading'])

const handleLoadMore = () => {
  emit('load-more')
}

const handleUpdateLoading = (value) => {
  emit('update:loading', value)
}

const handleItemClick = (item) => {
  emit('item-click', item)
}

const handleAddCart = (item) => {
  emit('add-cart', item)
}
</script>

<style scoped lang="less">
.goods-list-layout {
  .list-layout {
    margin-top: 2px;

    .goods-list-container {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
