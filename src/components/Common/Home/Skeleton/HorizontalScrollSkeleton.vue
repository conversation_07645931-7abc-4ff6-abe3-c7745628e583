<template>
  <div class="horizontal-scroll-skeleton">
    <div class="skeleton-scroll-wrapper">
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-title short"></div>
          <div class="skeleton-details">
            <div class="skeleton-price"></div>
            <div class="skeleton-sales"></div>
          </div>
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">
// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.horizontal-scroll-skeleton {
  // 与 BFHomeView.vue 中 .horizontal-scroll-container 样式保持一致
  position: relative;
  min-height: 180px; // 与实际容器的 min-height: 180px 保持一致
  display: flex;
  align-items: center;

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px; // 与实际的 gap: 12px 保持一致
    overflow-x: auto;
    padding-bottom: 8px; // 与实际的 padding-bottom: 8px 保持一致
    scroll-behavior: smooth;
    width: 100%;

    // 与实际组件的 .no-scrollbar() 样式保持一致
    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;

    .skeleton-item {
      flex: 0 0 160px; // 与实际的 flex: 0 0 160px 保持一致
      background: #fff;
      border-radius: 8px; // 与 ProductCard 的 border-radius: 8px 保持一致
      overflow: hidden;
      border: 1px solid #f0f0f0; // 与 ProductCard 的边框保持一致
      cursor: pointer;
      // 计算总高度：图片180px + 内容约100px = 280px
      height: 280px;

      // 最后一个元素添加右边距，与实际布局保持一致
      &:last-child {
        margin-right: 12px;
      }

      .skeleton-image {
        .skeleton-base();
        width: 100%;
        height: 180px; // 与 ProductCard 图片高度 180px 保持一致
        border-radius: 8px 8px 0 0; // 与 ProductCard 圆角保持一致
        background-color: #fafafa; // 与 ProductCard 图片背景色保持一致
      }

      .skeleton-content {
        // 与 ProductCard 的 .goods-info padding: 12px 保持一致
        padding: 12px;

        .skeleton-title {
          .skeleton-base();
          // 与 ProductCard 的 .goods-name font-size: 14px 对应
          height: 14px;
          margin-bottom: 8px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 8px; // 与第一行标题保持一致的间距
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px; // 与 ProductCard 的 margin-bottom: 6px 保持一致
          gap: 8px;

          .skeleton-price {
            .skeleton-base();
            // 与 ProductCard 的 .goods-price font-size: 16px 对应
            height: 16px;
            width: 60px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            .skeleton-base();
            // 与 ProductCard 的 .goods-sales font-size: 12px 对应
            height: 12px;
            width: 50px;
            border-radius: 6px;
          }
        }

        .skeleton-spec {
          .skeleton-base();
          // 与 ProductCard 的 .goods-spec font-size: 12px 对应
          height: 12px;
          width: 80%;
          border-radius: 4px; // 与 ProductCard 的 border-radius: 4px 保持一致
        }
      }
    }
  }
}

// 移动端适配 - 保持与桌面端一致的尺寸
@media (max-width: 375px) {
  .horizontal-scroll-skeleton {
    .skeleton-scroll-wrapper {
      gap: 12px; // 保持与桌面端一致

      .skeleton-item {
        flex: 0 0 160px; // 保持与桌面端一致
        height: 280px; // 保持与桌面端一致

        .skeleton-image {
          height: 180px; // 保持与 ProductCard 一致
        }

        .skeleton-content {
          padding: 12px; // 保持与 ProductCard 一致

          .skeleton-title {
            height: 14px;
            margin-bottom: 8px;

            &.short {
              margin-bottom: 8px;
            }
          }

          .skeleton-details {
            .skeleton-price {
              height: 16px;
              width: 60px;
            }

            .skeleton-sales {
              height: 12px;
              width: 50px;
            }
          }

          .skeleton-spec {
            height: 12px;
          }
        }
      }
    }
  }
}
</style>
