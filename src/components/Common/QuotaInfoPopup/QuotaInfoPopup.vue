<template>
  <div class="quota-info-popup">
    <van-popup class="quota-info-popup-content" v-model:show="show" round position="bottom" :style="{ height: '70%' }"
      @close="onClose">
      <div class="quota-info-popup-title">慰问活动</div>
      <div class="quota-info-popup-container">
        <div class="quota-info-popup-tabs">
          <van-tabs ref="popupTabs" v-model:active="active" @change="onChange">
            <van-tab title="可用活动">
              <div class="quota-info-content"
                v-if="availableActivityQuotaList && availableActivityQuotaList.length > 0">
                <div class="quota-info-tips">
                  <img class="tips-icon" src="./assets/tips.png" alt="tips">
                  <span class="tips-content">已为您展示可用优惠，可在支付时选择</span>
                </div>
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <template v-if="grantAmount <= 0">
                    <div class="quota-info-card" v-for="item in availableActivityQuotaList" :key="item.activityNo">
                      <div class="quota-money">{{ fenToYuan(item.balanceAmount) }}</div>
                      <div class="quota-info">
                        <h3 class="active-name">{{ item.activityName }}</h3>
                        <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      </div>
                    </div>
                  </template>
                  <template v-if="grantAmount > 0">
                    <div class="quota-info-special">
                      <h3 class="active-name">{{ availableActivityQuota.activityName }}</h3>
                      <div class="quota-money-card">
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.grantAmount) }}</div>
                          <div class="quota-money-type">已发放积点</div>
                        </div>
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.usedAmount) }}</div>
                          <div class="quota-money-type">已使用积点</div>
                        </div>
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.amount) }}</div>
                          <div class="quota-money-type">剩余积点</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
            <van-tab title="不可用活动">
              <div class="quota-info-content quota-info-content-no"
                v-if="unAvailableActivityQuotaList && unAvailableActivityQuotaList.length > 0">
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <div class="quota-info-card quota-info-card-no" v-for="item in unAvailableActivityQuotaList"
                    :key="item.activityNo">
                    <div class="quota-money">{{ fenToYuan(item.balanceAmount) }}</div>
                    <div class="quota-info">
                      <h3 class="active-name">{{ item.activityName }}</h3>
                      <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      <p class="active-unavailable">不可用原因：活动不适用于该商城</p>
                    </div>
                  </div>
                </div>
              </div>
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
          </van-tabs>
        </div>
      </div>
      <WoActionBar class="quota-info-op">
        <WoButton type="primary" block size="xlarge" @click="onClose">确定</WoButton>
      </WoActionBar>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import { fenToYuan } from '@/utils/amount'
import { get, defaultTo } from 'lodash'
import noActive from './assets/no-active.png'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  availableActivityQuota: {
    type: Object,
    default: () => ({})
  },
  unAvailableActivityQuota: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const popupTabs = ref(null)
const quotaInfoDetail = ref(null)

const noDataImg = ref(noActive)
const show = ref(false)
const active = ref(0)

const availableActivityQuotaList = computed(() => {
  const quotaInfo = get(props.availableActivityQuota, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

const unAvailableActivityQuotaList = computed(() => {
  const quotaInfo = get(props.unAvailableActivityQuota, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

const grantAmount = computed(() => {
  // 使用模拟数据替代真实数据
  return props.availableActivityQuota?.grantAmount || 0
})

watch(
  () => props.modelValue,
  (val) => {
    show.value = val
  },
  { immediate: true }
)

const onClose = () => {
  show.value = false
  emit('update:modelValue', false)
}

const onChange = (name) => {
  active.value = name
  emit('change', name)
}
</script>

<style scoped lang="less">
@import '@/assets/css/design-system.less';

.quota-info-popup :deep(.van-popup) {
  box-sizing: border-box;
  padding: 16px;
}

.quota-info-popup {
  height: 100%;

  .quota-info-popup-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .quota-info-op {
      width: 100%;
      margin-top: 16px;
      flex-shrink: 0;

      // 覆盖WoActionBar的固定定位，在弹窗中使用相对定位
      :deep(.action-bar) {
        position: relative;
        left: auto;
        right: auto;
        bottom: auto;
        padding: 0;
        background-color: transparent;
        box-shadow: none;
        z-index: auto;
        min-height: auto;
      }
    }
  }

  .quota-info-popup-title {
    margin-bottom: 16px;
    font-size: @font-size-18;
    color: @text-color-primary;
    font-weight: @font-weight-600;
    text-align: center;
    flex-shrink: 0;
  }

  .quota-info-popup-container {
    width: 100%;
    flex: 1;
    overflow: hidden;
    min-height: 0;

    .quota-info-popup-tabs {
      width: 100%;
      height: 100%;

      :deep(.van-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      :deep(.van-tabs__wrap) {
        height: 44px;
        flex-shrink: 0;
      }

      :deep(.van-tabs__content) {
        flex: 1;
        overflow: hidden;
      }

      :deep(.van-tab__pane) {
        height: 100%;
        overflow: hidden;
      }

      :deep(.van-tabs__line) {
        width: 30px !important;
        height: 2px !important;
        background: @theme-color !important;
        border-radius: 1px !important;
      }

      :deep(.van-tab--active .van-tab__text) {
        font-size: @font-size-15;
        color: @theme-color !important;
        line-height: 1.5;
        font-weight: @font-weight-600 !important;
      }

      :deep(.van-tab .van-tab__text) {
        font-size: @font-size-15;
        line-height: 1.5;
        font-weight: @font-weight-400;
        color: @text-color-secondary;
      }

      :deep(.van-empty) {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 40px 20px !important;
      }
    }

    .quota-info-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 12px 0 0;
      box-sizing: border-box;

      .quota-info-tips {
        display: flex;
        align-items: center;
        padding: 10px 12px;
        margin-bottom: 12px;
        background: @bg-color-tips;
        border: 1px solid @border-color-tips;
        border-radius: @radius-6;
        flex-shrink: 0;

        .tips-icon {
          margin-right: 8px;
          width: 16px;
          height: 16px;
          flex-shrink: 0;
        }

        .tips-content {
          font-size: @font-size-13;
          color: @theme-color;
          font-weight: @font-weight-400;
          line-height: 1.4;
        }
      }

      .quota-info-detail {
        flex: 1;
        overflow-y: auto;
        padding-right: 2px;
        min-height: 0;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background: @bg-color-gray;
          border-radius: @radius-2;
        }

        &::-webkit-scrollbar-thumb {
          background: @text-color-disabled;
          border-radius: @radius-2;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: @text-color-tertiary;
        }

        .quota-info-card {
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          background: @bg-color-white;
          border: 1px solid @divider-color-base;
          border-radius: @radius-8;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          // 左侧红色装饰条
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: @theme-color;
          }

          .quota-money {
            display: flex;
            align-items: baseline;
            justify-content: center;
            min-width: 90px;
            padding: 16px 12px;
            font-size: @font-size-20;
            color: @theme-color;
            line-height: 1.2;
            font-weight: @font-weight-700;
            flex-shrink: 0;
            text-align: center;

            &::before {
              content: '￥';
              font-size: @font-size-14;
              font-weight: @font-weight-500;
              margin-right: 2px;
            }
          }

          .quota-info {
            flex: 1;
            min-width: 0;
            padding: 16px 16px 16px 12px;

            .active-name {
              margin-bottom: 8px;
              font-size: @font-size-15;
              color: @text-color-primary;
              line-height: 1.4;
              font-weight: @font-weight-500;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              text-overflow: ellipsis;
              word-break: break-all;
            }

            .active-validity-period {
              font-size: @font-size-12;
              color: @text-color-tertiary;
              font-weight: @font-weight-400;
              line-height: 1.4;
              margin-bottom: 0;
            }

            .active-unavailable {
              font-size: @font-size-12;
              color: @text-color-secondary;
              line-height: 1.4;
              font-weight: @font-weight-400;
              margin-top: 4px;
            }
          }
        }

        .quota-info-special {
          position: relative;
          box-sizing: border-box;
          padding: 16px;
          margin-bottom: 12px;
          background: @bg-color-white;
          border: 1px solid @divider-color-base;
          border-radius: @radius-8;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          // 左侧红色装饰条
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: @theme-color;
          }

          .active-name {
            margin-bottom: 16px;
            font-size: @font-size-16;
            color: @text-color-primary;
            line-height: 1.4;
            font-weight: @font-weight-600;
            text-align: center;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
          }

          .quota-money-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
            background: @bg-color-gray;
            border-radius: @radius-6;
            padding: 12px 8px;

            .quota-money-item {
              flex: 1;
              text-align: center;
              position: relative;

              &:not(:last-child)::after {
                content: '';
                position: absolute;
                right: -6px;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 24px;
                background: @divider-color-base;
              }

              .quota-money-number {
                display: flex;
                align-items: baseline;
                justify-content: center;
                font-size: @font-size-18;
                color: @theme-color;
                font-weight: @font-weight-700;
                margin-bottom: 4px;
                line-height: 1.2;

                &::before {
                  content: '￥';
                  font-size: @font-size-13;
                  font-weight: @font-weight-500;
                  margin-right: 1px;
                }
              }

              .quota-money-type {
                font-size: @font-size-12;
                color: @text-color-tertiary;
                line-height: 1.2;
                font-weight: @font-weight-400;
              }
            }
          }
        }

        .quota-info-card-no {
          background: @bg-color-gray;
          border-color: @divider-color-base;
          opacity: @opacity-07;

          &::before {
            background: @text-color-disabled;
          }

          .quota-money {
            color: @text-color-tertiary;

            &::before {
              color: @text-color-tertiary;
            }
          }

          .quota-info {
            .active-name {
              color: @text-color-tertiary;
            }

            .active-validity-period {
              color: @text-color-disabled;
            }

            .active-unavailable {
              color: @text-color-tertiary;
            }
          }
        }
      }
    }

    .quota-info-content-no {
      padding-top: 12px;
    }
  }
}
</style>
