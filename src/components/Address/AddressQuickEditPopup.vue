<template>
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%' }" @close="handleClose">
    <div class="address-edit-popup">
      <div class="popup-header">
        <h3 class="title">编辑原地址</h3>
        <div class="close-btn" @click="handleClose">×</div>
      </div>
      <div class="popup-content">
        <AddressForm
          ref="addressFormRef"
          :initial-data="initialData"
          @validate="onFormValidate"
          @region-change="onRegionChange"
        />
      </div>
      <WoActionBar class="action-bar">
        <WoButton type="primary" block size="xlarge" @click="handleSave">保存</WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, toRefs, defineProps, defineEmits } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressForm from '@components/Address/AddressForm.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits([
  'close',
  'save',
  'validate',
  'region-change',
  'update:visible'
])

const { visible, initialData } = toRefs(props)

// 组件引用
const addressFormRef = ref(null)

// 表单验证回调
const onFormValidate = ({ isValid }) => {
  emit('validate', { isValid })
}

// 地区变化回调
const onRegionChange = (regionData) => {
  emit('region-change', regionData)
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleSave = () => {
  emit('save', addressFormRef.value)
}

defineExpose({
  addressFormRef
})
</script>

<style scoped lang="less">
.address-edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: @bg-color-white;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    //border-bottom: 1px solid @divider-color-base;

    .title {
      font-size: @font-size-17;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      margin: 0;
    }

    .close-btn {
      font-size: 24px;
      color: @text-color-tertiary;
      cursor: pointer;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .popup-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }


}
</style>
