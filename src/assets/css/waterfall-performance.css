/* 
 * Waterfall 组件性能优化 - 禁用所有动画
 * 用于提升加载性能，避免动画影响渲染
 */

/* 禁用 vue-waterfall-plugin-next 的所有动画和过渡效果 */
.vue-waterfall-container * {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* 禁用瀑布流容器的动画 */
.vue-waterfall-container {
  transition: none !important;
  animation: none !important;
}

/* 禁用瀑布流项目的动画 */
.vue-waterfall-item {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* 禁用瀑布流项目的进入动画 */
.vue-waterfall-item-enter-active,
.vue-waterfall-item-leave-active,
.vue-waterfall-item-move {
  transition: none !important;
  animation: none !important;
}

.vue-waterfall-item-enter-from,
.vue-waterfall-item-leave-to {
  opacity: 1 !important;
  transform: none !important;
}

/* 禁用可能的淡入淡出效果 */
.waterfall-fade-enter-active,
.waterfall-fade-leave-active,
.waterfall-slide-enter-active,
.waterfall-slide-leave-active {
  transition: none !important;
  animation: none !important;
}

.waterfall-fade-enter-from,
.waterfall-fade-leave-to,
.waterfall-slide-enter-from,
.waterfall-slide-leave-to {
  opacity: 1 !important;
  transform: none !important;
}

/* 禁用图片加载动画 */
.vue-waterfall-container img {
  transition: none !important;
  animation: none !important;
}

/* 禁用懒加载相关动画 */
.lazy-img-enter-active,
.lazy-img-leave-active {
  transition: none !important;
  animation: none !important;
}

.lazy-img-enter-from,
.lazy-img-leave-to {
  opacity: 1 !important;
  transform: none !important;
}

/* 禁用可能的缩放动画 */
.vue-waterfall-container .scale-enter-active,
.vue-waterfall-container .scale-leave-active {
  transition: none !important;
  animation: none !important;
}

.vue-waterfall-container .scale-enter-from,
.vue-waterfall-container .scale-leave-to {
  opacity: 1 !important;
  transform: none !important;
}

/* 确保所有子元素都没有动画 */
.vue-waterfall-container *,
.vue-waterfall-container *::before,
.vue-waterfall-container *::after {
  transition: none !important;
  animation: none !important;
  transform: none !important;
}

/* 针对可能的第三方动画库的覆盖 */
.vue-waterfall-container .animate__animated {
  animation: none !important;
}

/* 禁用可能的 CSS 变量动画 */
.vue-waterfall-container {
  --transition-duration: 0s !important;
  --animation-duration: 0s !important;
}

/* 性能优化：强制使用 GPU 加速但不使用动画 */
.vue-waterfall-container {
  will-change: auto !important;
  backface-visibility: visible !important;
}

.vue-waterfall-item {
  will-change: auto !important;
  backface-visibility: visible !important;
}

/* 禁用可能的 hover 动画 */
.vue-waterfall-container *:hover {
  transition: none !important;
  animation: none !important;
}

/* 确保在移动设备上也禁用动画 */
@media (max-width: 768px) {
  .vue-waterfall-container *,
  .vue-waterfall-container *::before,
  .vue-waterfall-container *::after {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
}

/* 针对可能的自定义动画类名 */
.waterfall-enter,
.waterfall-enter-active,
.waterfall-enter-to,
.waterfall-leave,
.waterfall-leave-active,
.waterfall-leave-to,
.waterfall-move {
  transition: none !important;
  animation: none !important;
  transform: none !important;
  opacity: 1 !important;
}

/* 禁用可能的加载状态动画 */
.vue-waterfall-loading,
.vue-waterfall-loading * {
  transition: none !important;
  animation: none !important;
}

/* 确保瀑布流重新布局时没有动画 */
.vue-waterfall-container[data-loading="true"] *,
.vue-waterfall-container[data-updating="true"] * {
  transition: none !important;
  animation: none !important;
}
