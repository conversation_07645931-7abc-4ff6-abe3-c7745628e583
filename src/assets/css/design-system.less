// 设计规范 - 基于750设计稿 （前端手动转化为375）

// ======== 一、颜色变量 ========

// 主题色
@theme-color: #FF7A0A; // 沃橙色/品牌橙主色
// @theme-color: #FF2F2F;    // 京东主题色

// 功能色
@color-red: #FF2F2F; // 红色
@color-green: #00C35A; // 绿色
@color-yellow: #FFA300; // 黄色
@color-orange: #FF7A0A; // 沃橙色
@color-white: #FFFFFF;

// 渐变色
@gradient-orange-106: linear-gradient(106deg, #FFA033 0%, #FF6D33 100%); // 按钮渐变色 106度
@gradient-orange-115: linear-gradient(115deg, #FFA033 0%, #FF6D33 100%); // 搜索按钮渐变色 115度
@gradient-orange-dark: linear-gradient(106deg, darken(#FFA033, 10%) 0%, darken(#FF6D33, 10%) 100%); // 按钮激活状态渐变色

// 中性色 - 文字
@text-color-primary: #171E24; // 文字色-1 主要文字
@text-color-secondary: #5A6066; // 文字色-2 次重要文字
@text-color-tertiary: #879099; // 文字色-3 提示文字、辅助信息
@text-color-disabled: #CED0D8; // 文字色-4 弱提示、输入框引导、禁用
@text-color-white: #FFFFFF; // 文字色-5 输入框提示文字
@text-color-tips: #FF4141;
; // 文字色-6 提示文字
// 中性色 - 背景
@bg-color-gray: #F8F9FA; // 背景色-1
@bg-color-white: #FFFFFF; // 背景色-2
@bg-color-tips: #fff7f0; // 提示背景色
@border-color-tips: #ffe4d1; // 提示边框色

// ======== 二、字体变量 ========

// 字体家族  价格数字特殊处理
@font-family-base: D-DIN-PRO-SemiBold, OPPOSans-R, PingFangSC-Regular, sans-serif;

// 字号
@font-size-22: 22px; // 最大
@font-size-20: 20px; // 一级标题 订单、支付状态
@font-size-18: 18px; // 二级标题 模块标题
@font-size-17: 17px; // 三级标题 弹窗、内容、卡片标题
@font-size-16: 16px; // 名字电话地址等
@font-size-15: 15px; // 输入框、可操作表单等
@font-size-14: 14px; // 输入框、可操作表单等
@font-size-13: 13px; // 商品名称、地址、列表信息等
@font-size-12: 12px; // Toast文案、辅助文字
@font-size-11: 11px; // Tag标签文字、弱提示信息


// 行高
@line-height-20: 20px;

// 字重
@font-weight-400: 400;
@font-weight-500: 500;
@font-weight-600: 600;
@font-weight-700: 700;

// 分割线
@divider-color-base: #E2E8EE;

// 遮罩
@mask-color-065: rgba(0, 0, 0, 0.65);
@mask-color-05: rgba(255, 255, 255, 0.5);
// ======== 三、尺寸变量 ========

// 圆角
@radius-2: 2px; // 标签、小按钮
@radius-4: 4px; // 商品图片容器
@radius-6: 6px; // 瓷片区、大背景块
@radius-8: 8px; // 瓷片区、大背景块
@radius-10: 10px; // 模块
@radius-12: 12px; // 底部弹层、弹窗
@radius-9999: 9999px; // 主按钮、搜索框
@radius-15: 15px; // 小按钮圆角
@radius-18: 18px; // 中等按钮圆角
@radius-20: 20px; // 特殊按钮圆角
@radius-22: 22px; // 大按钮圆角
@radius-50: 50px; // 圆形按钮

// 间距
@padding-page: 5px; // 原10px 页面内边距
@padding-button: 15px; // 按钮内边距

// 透明度
@opacity-065: 0.65; // 65% 页面蒙层透明度
@opacity-05: 0.5; // 50% 失效商品展示透明度
@opacity-07: 0.7; // 70% 文本按钮激活透明度

// ======== 四、按钮变量 ========

// 按钮尺寸
@button-height-42: 42px; // 原84px 主按钮高度
@button-height-38: 38px; // 原72px 次按钮高度
@button-height-36: 36px; // 原72px
@button-height-32: 32px; // 原64px
@button-height-28: 28px; // 原56px
@button-height-24: 24px; // 原48px
@button-width-160: 160px; // 大按钮宽度
@button-width-119: 119px; // 特殊按钮宽度
@button-width-90: 90px; // 中等按钮宽度
@button-width-80: 80px; // 小按钮宽度

// ======== 五、混合(Mixins) ========

// 文本溢出省略号
.ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出省略号
.multi-ellipsis(@lines) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-all
}

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 去除滚动条
.no-scrollbar() {
  &::-webkit-scrollbar {
    width: 0 !important;
    height: 0;
  }
}

// 按钮基础样式
.button-base() {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  border: none;
  cursor: pointer;
  border-radius: @radius-9999;
}

// 主按钮样式
.button-primary() {
  .button-base();
  height: @button-height-42;
  border-radius: @button-height-42 / 2;
  background-color: @color-orange;
  color: @bg-color-white;
  font-size: @font-size-16;
  font-weight: @font-weight-500;
}

// 次按钮样式
.button-secondary() {
  .button-base();
  height: @button-height-32;
  border-radius: @button-height-32 / 2;
  background-color: @bg-color-white;
  color: @color-orange;
  border: 0.5px solid @color-orange;
  font-size: @font-size-16;
}

// 小按钮样式
.button-small() {
  .button-base();
  height: @button-height-24;
  border-radius: @button-height-24 / 2;
  font-size: @font-size-14;
}

// ======== 六、通用样式类 ========

// 字体样式类
.text-primary {
  color: @text-color-primary;
}

.text-secondary {
  color: @text-color-secondary;
}

.text-tertiary {
  color: @text-color-tertiary;
}

.text-disabled {
  color: @text-color-disabled;
}

// 字号样式类
.font-11 {
  font-size: @font-size-11;
}

.font-12 {
  font-size: @font-size-12;
}

.font-13 {
  font-size: @font-size-13;
}

.font-14 {
  font-size: @font-size-14;
}

.font-16 {
  font-size: @font-size-16;
}

.font-17 {
  font-size: @font-size-17;
}

.font-18 {
  font-size: @font-size-18;
}

.font-20 {
  font-size: @font-size-20;
}

// 字重样式类
.font-regular {
  font-weight: @font-weight-400;
}

.font-medium {
  font-weight: @font-weight-500;
}

.font-bold {
  font-weight: @font-weight-700;
}

// 标题样式类
.title-level-1 {
  font-size: @font-size-20;
  font-weight: @font-weight-700;
  color: @text-color-primary;
  line-height: @line-height-20;
}

.title-level-2 {
  font-size: @font-size-18;
  font-weight: @font-weight-700;
  color: @text-color-primary;
  line-height: @line-height-20;
}

.title-level-3 {
  font-size: @font-size-17;
  font-weight: @font-weight-500;
  color: @text-color-primary;
  line-height: @line-height-20;
}

// 文本样式类
.text-body {
  font-size: @font-size-16;
  font-weight: @font-weight-500;
  color: @text-color-primary;
  line-height: @line-height-20;
}

.text-caption {
  font-size: @font-size-13;
  font-weight: @font-weight-400;
  color: @text-color-tertiary;
  line-height: @line-height-20;
}

.text-note {
  font-size: @font-size-11;
  font-weight: @font-weight-400;
  color: @text-color-tertiary;
  line-height: @line-height-20;
}

// 圆角样式类
.radius-xs {
  border-radius: @radius-2;
}

.radius-sm {
  border-radius: @radius-4;
}

.radius-md {
  border-radius: @radius-8;
}

.radius-lg {
  border-radius: @radius-12;
}

.radius-circle {
  border-radius: @radius-9999;
}

// 按钮样式类
.btn-primary {
  .button-primary();
}

.btn-secondary {
  .button-secondary();
}

.btn-small {
  .button-small();
}

// 去除滚动条
::-webkit-scrollbar {
  width: 0 !important;
  height: 0;
}

// 全局基础样式
body {
  font-family: @font-family-base;
  font-size: @font-size-16;
  color: @text-color-primary;
  background-color: @bg-color-white;
  line-height: @line-height-20;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
