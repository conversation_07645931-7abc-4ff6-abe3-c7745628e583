export const baseRoutes = [
  {
    path: '/login',
    name: 'login',
    meta: {},
    component: () => import('@views/Base/LoginView.vue')
  },
  {
    path: '/addr/add',
    name: 'address-add',
    meta: { login: true },
    component: () => import('@views/WoMall/Address/AddressAddOrEdit.vue'),
  },
  {
    path: '/addr/edit',
    name: 'address-edit',
    meta: { login: true },
    component: () => import('@views/WoMall/Address/AddressAddOrEdit.vue'),
  },
  {
    path: '/user/order/address-edit',
    name: 'order-address-edit',
    meta: { login: true },
    component: () => import('@views/WoMall/Address/OrderAddressAddOrEdit.vue'),
  },
  {
    path: '/addr/list',
    name: 'address-list',
    meta: { login: true },
    component: () => import('@views/WoMall/Address/AddressList.vue'),
  },
  {
    path: '/cart',
    name: 'cart',
    meta: { title: '购物车' },
    component: () => import('@views/WoMall/Cart/CartView.vue'),
  },
  {
    path: '/category/:id?',
    name: 'category',
    component: () => import('@views/WoMall/Category/CategoryView.vue'),
  },
  {
    path: '/user',
    name: 'user',
    component: () => import('@views/WoMall/User/IndexView.vue'),
  },
  {
    path: '/user/help',
    name: 'user-help',
    meta: { title: '帮助中心' },
    component: () => import('@views/WoMall/User/UserHelp.vue'),
  },
  {
    path: '/search',
    name: 'search',
    component: () => import('@views/WoMall/Search/IndexView.vue'),
  },
  {
    path: '/search/list',
    name: 'search-list',
    meta: {
      login: true
    },
    component: () => import('@views/WoMall/Search/SearchList.vue'),
  },
]
