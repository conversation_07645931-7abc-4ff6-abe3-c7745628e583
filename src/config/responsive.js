/**
 * 响应式断点配置
 * 统一管理项目中的响应式布局断点
 */

/**
 * 标准响应式断点配置
 * 基于常见设备屏幕尺寸定义
 */
export const DEFAULT_BREAKPOINTS = {
  // 超大屏设备 (大平板横屏、桌面)
  1024: { rowPerView: 5 },

  // 大平板设备 (iPad Pro 12.9", iPad Air 横屏)
  834: { rowPerView: 4 },

  // 标准平板设备 (iPad 竖屏, 小平板横屏)
  768: { rowPerView: 4 },

  // 大屏手机横屏 & 小平板竖屏
  667: { rowPerView: 3 },

  // 大屏手机 (iPhone 14 Pro Max, iPhone 13 Pro Max 等)
  430: { rowPerView: 3 },

  // 标准大屏手机 (iPhone 14 Pro, iPhone 13 Pro, 大部分安卓旗舰)
  414: { rowPerView: 2 },

  // 中等屏幕手机 (iPhone 12/13 mini, 中端安卓)
  390: { rowPerView: 2 },

  // 标准屏幕手机 (iPhone SE 3rd, 大部分安卓中端机)
  375: { rowPerView: 2 },

  // 小屏手机 (老款 iPhone, 小屏安卓)
  360: { rowPerView: 2 },

  // 超小屏手机 (iPhone SE 1st/2nd, 老款安卓)
  320: { rowPerView: 2 },

  // 极小屏设备兜底 (折叠屏折叠状态等)
  0: { rowPerView: 1 }
}

/**
 * 获取默认断点配置
 * @returns {Object} 断点配置对象
 */
export const getDefaultBreakpoints = () => {
  return { ...DEFAULT_BREAKPOINTS }
}

/**
 * 创建自定义断点配置
 * @param {Object} customBreakpoints - 自定义断点配置
 * @returns {Object} 合并后的断点配置
 */
export const createBreakpoints = (customBreakpoints = {}) => {
  return {
    ...DEFAULT_BREAKPOINTS,
    ...customBreakpoints
  }
}

/**
 * 根据屏幕宽度获取对应的 rowPerView 值
 * @param {number} width - 屏幕宽度
 * @param {Object} breakpoints - 断点配置，默认使用 DEFAULT_BREAKPOINTS
 * @returns {number} rowPerView 值
 */
export const getRowPerViewByWidth = (width, breakpoints = DEFAULT_BREAKPOINTS) => {
  const sortedBreakpoints = Object.keys(breakpoints)
    .map(Number)
    .sort((a, b) => b - a) // 从大到小排序

  for (const breakpoint of sortedBreakpoints) {
    if (width >= breakpoint) {
      return breakpoints[breakpoint].rowPerView
    }
  }

  // 如果没有匹配的断点，返回最小断点的值
  return breakpoints[0]?.rowPerView || 1
}

/**
 * 响应式断点常量
 */
export const BREAKPOINT_VALUES = {
  DESKTOP_LARGE: 1024,
  TABLET_LARGE: 834,
  TABLET_STANDARD: 768,
  MOBILE_LANDSCAPE: 667,
  MOBILE_LARGE: 430,
  MOBILE_STANDARD: 414,
  MOBILE_MEDIUM: 390,
  MOBILE_SMALL: 375,
  MOBILE_TINY: 360,
  MOBILE_MINI: 320,
  MOBILE_MICRO: 0
}

/**
 * 根据设备类型获取推荐的 rowPerView 值
 */
export const ROW_PER_VIEW_PRESETS = {
  DESKTOP: 5,
  TABLET: 4,
  MOBILE_LANDSCAPE: 3,
  MOBILE_PORTRAIT: 2,
  MOBILE_SMALL: 1
}

/**
 * 创建瀑布流专用的断点配置
 * @param {Object} options - 配置选项
 * @param {number} options.desktopColumns - 桌面端列数，默认5
 * @param {number} options.tabletColumns - 平板端列数，默认4
 * @param {number} options.mobileColumns - 手机端列数，默认2
 * @returns {Object} 瀑布流断点配置
 */
export const createWaterfallBreakpoints = (options = {}) => {
  const {
    desktopColumns = ROW_PER_VIEW_PRESETS.DESKTOP,
    tabletColumns = ROW_PER_VIEW_PRESETS.TABLET,
    mobileColumns = ROW_PER_VIEW_PRESETS.MOBILE_PORTRAIT
  } = options

  return {
    [BREAKPOINT_VALUES.DESKTOP_LARGE]: { rowPerView: desktopColumns },
    [BREAKPOINT_VALUES.TABLET_LARGE]: { rowPerView: tabletColumns },
    [BREAKPOINT_VALUES.TABLET_STANDARD]: { rowPerView: tabletColumns },
    [BREAKPOINT_VALUES.MOBILE_LANDSCAPE]: { rowPerView: Math.max(mobileColumns + 1, 3) },
    [BREAKPOINT_VALUES.MOBILE_LARGE]: { rowPerView: Math.max(mobileColumns + 1, 3) },
    [BREAKPOINT_VALUES.MOBILE_STANDARD]: { rowPerView: mobileColumns },
    [BREAKPOINT_VALUES.MOBILE_MEDIUM]: { rowPerView: mobileColumns },
    [BREAKPOINT_VALUES.MOBILE_SMALL]: { rowPerView: mobileColumns },
    [BREAKPOINT_VALUES.MOBILE_TINY]: { rowPerView: mobileColumns },
    [BREAKPOINT_VALUES.MOBILE_MINI]: { rowPerView: mobileColumns },
    [BREAKPOINT_VALUES.MOBILE_MICRO]: { rowPerView: Math.max(mobileColumns - 1, 1) }
  }
}

/**
 * 检查当前屏幕是否为移动设备
 * @param {number} width - 屏幕宽度，默认使用 window.innerWidth
 * @returns {boolean} 是否为移动设备
 */
export const isMobileDevice = (width = typeof window !== 'undefined' ? window.innerWidth : 375) => {
  return width < BREAKPOINT_VALUES.TABLET_STANDARD
}

/**
 * 检查当前屏幕是否为平板设备
 * @param {number} width - 屏幕宽度，默认使用 window.innerWidth
 * @returns {boolean} 是否为平板设备
 */
export const isTabletDevice = (width = typeof window !== 'undefined' ? window.innerWidth : 768) => {
  return width >= BREAKPOINT_VALUES.TABLET_STANDARD && width < BREAKPOINT_VALUES.DESKTOP_LARGE
}

/**
 * 检查当前屏幕是否为桌面设备
 * @param {number} width - 屏幕宽度，默认使用 window.innerWidth
 * @returns {boolean} 是否为桌面设备
 */
export const isDesktopDevice = (width = typeof window !== 'undefined' ? window.innerWidth : 1024) => {
  return width >= BREAKPOINT_VALUES.DESKTOP_LARGE
}
